#!/usr/bin/env python3
# 创建与ESP32兼容的音频文件
import sys
import os
import struct
import numpy as np
import wave
import argparse

def create_compatible_audio(output_file, duration=3, sample_rate=16000):
    """创建与ESP32完全兼容的音频文件"""
    try:
        print(f"正在创建兼容的音频文件: {output_file}")
        
        # 生成清晰的测试音频信号
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        
        # 创建多频率混合音频（模拟语音特征）
        audio_data = (
            0.4 * np.sin(2 * np.pi * 440 * t) +      # A4音符
            0.3 * np.sin(2 * np.pi * 880 * t) +      # A5音符
            0.2 * np.sin(2 * np.pi * 1320 * t) +     # E6音符
            0.1 * np.sin(2 * np.pi * 220 * t)        # A3音符
        )
        
        # 应用包络以避免突然的开始和结束
        fade_samples = int(0.1 * sample_rate)  # 100ms淡入淡出
        fade_in = np.linspace(0, 1, fade_samples)
        fade_out = np.linspace(1, 0, fade_samples)
        
        audio_data[:fade_samples] *= fade_in
        audio_data[-fade_samples:] *= fade_out
        
        # 归一化到合适的音量
        audio_data = audio_data / np.max(np.abs(audio_data)) * 0.7
        
        # 转换为16位整数
        audio_data = (audio_data * 32767).astype(np.int16)
        
        # 写入WAV文件
        with wave.open(output_file, 'wb') as wav_file:
            wav_file.setnchannels(1)  # 单声道
            wav_file.setsampwidth(2)  # 16位
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(audio_data.tobytes())
        
        print(f"音频文件已创建: {output_file}")
        print(f"时长: {duration}秒, 采样率: {sample_rate}Hz, 样本数: {len(audio_data)}")
        return True
        
    except Exception as e:
        print(f"创建音频失败: {e}")
        return False

def wav_to_simple_p3(wav_file, p3_file):
    """将WAV文件转换为简化的P3格式（与ESP32兼容）"""
    try:
        # 读取WAV文件
        with wave.open(wav_file, 'rb') as wav:
            frames = wav.readframes(wav.getnframes())
            sample_rate = wav.getframerate()
            channels = wav.getnchannels()
            
        print(f"读取WAV文件: {wav_file}")
        print(f"采样率: {sample_rate}Hz, 声道: {channels}")
        
        # 转换为16位整数数组
        audio_data = np.frombuffer(frames, dtype=np.int16)
        
        # 创建简化的P3格式（与现有系统兼容）
        with open(p3_file, 'wb') as f:
            # 使用较小的帧大小以提高兼容性
            frame_duration_ms = 20  # 20ms帧（更标准）
            frame_size = int(sample_rate * frame_duration_ms / 1000)
            
            packet_count = 0
            total_bytes = 0
            
            for i in range(0, len(audio_data) - frame_size, frame_size):
                frame = audio_data[i:i + frame_size]
                
                # 简化的包头：版本(1字节) + 标志(1字节) + 数据长度(2字节) + 数据
                frame_bytes = frame.tobytes()
                packet_header = struct.pack('<BBH', 1, 0, len(frame_bytes))  # 使用小端序
                packet = packet_header + frame_bytes
                
                f.write(packet)
                packet_count += 1
                total_bytes += len(packet)
        
        print(f"P3格式文件已创建: {p3_file}")
        print(f"包数量: {packet_count}, 总大小: {total_bytes} 字节")
        print(f"帧大小: {frame_size} 样本 ({frame_duration_ms}ms)")
        return True
        
    except Exception as e:
        print(f"转换为P3格式失败: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='创建与ESP32兼容的音频文件')
    parser.add_argument('-o', '--output', default='voice/compatible_audio.wav', help='输出WAV文件')
    parser.add_argument('-p', '--p3', default='voice/compatible_audio.p3', help='输出P3文件')
    parser.add_argument('-d', '--duration', type=float, default=3.0, help='音频时长（秒）')
    
    args = parser.parse_args()
    
    print("创建与ESP32兼容的音频文件...")
    
    # 第一步：创建WAV文件
    if not create_compatible_audio(args.output, args.duration):
        return False
    
    # 第二步：转换为P3格式
    if not wav_to_simple_p3(args.output, args.p3):
        return False
    
    print(f"完成！兼容的音频文件已创建：")
    print(f"  WAV: {args.output}")
    print(f"  P3:  {args.p3}")
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
