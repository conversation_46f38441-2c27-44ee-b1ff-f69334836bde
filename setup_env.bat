@echo off
echo Setting up ESP-IDF environment...

REM Set ESP-IDF path
set IDF_PATH=D:\ESP_IDF\5.4.2\v5.4.2\esp-idf

REM Add Python environment to PATH
set PATH=D:\ESP_IDF\5.4.2\TOOLS\python_env\idf5.4_py3.11_env\Scripts;%PATH%

REM Add ESP-IDF tools to PATH
set PATH=D:\ESP_IDF\5.4.2\TOOLS\tools\xtensa-esp-elf\esp-14.2.0_20241119\xtensa-esp-elf\bin;%PATH%
set PATH=D:\ESP_IDF\5.4.2\TOOLS\tools\riscv32-esp-elf\esp-14.2.0_20241119\riscv32-esp-elf\bin;%PATH%
set PATH=D:\ESP_IDF\5.4.2\TOOLS\tools\esp32ulp-elf\2.38_20240113\esp32ulp-elf\bin;%PATH%
set PATH=D:\ESP_IDF\5.4.2\TOOLS\tools\cmake\3.24.0\bin;%PATH%
set PATH=D:\ESP_IDF\5.4.2\TOOLS\tools\ninja\1.11.1;%PATH%

echo Environment set up complete.
echo IDF_PATH=%IDF_PATH%

REM Now run the commands
echo Cleaning build directory...
rmdir /s /q build 2>nul

echo Setting target to ESP32S3...
python %IDF_PATH%\tools\idf.py set-target esp32s3

if %ERRORLEVEL% NEQ 0 (
    echo Failed to set target!
    pause
    exit /b 1
)

echo Building project...
python %IDF_PATH%\tools\idf.py build

if %ERRORLEVEL% NEQ 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Flashing to device...
python %IDF_PATH%\tools\idf.py -p COM5 flash

if %ERRORLEVEL% NEQ 0 (
    echo Flash failed!
    pause
    exit /b 1
)

echo Success! Starting monitor...
python %IDF_PATH%\tools\idf.py -p COM5 monitor
