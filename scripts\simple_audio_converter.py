#!/usr/bin/env python3
# 简化的音频转换工具
import sys
import os
from pydub import AudioSegment
import argparse

def convert_audio_to_wav(input_file, output_file):
    """使用pydub将音频转换为WAV格式"""
    try:
        print(f"正在加载音频文件: {input_file}")
        
        # 加载音频文件
        audio = AudioSegment.from_file(input_file)
        
        # 转换为单声道
        if audio.channels > 1:
            audio = audio.set_channels(1)
            print("已转换为单声道")
        
        # 设置采样率为16kHz
        if audio.frame_rate != 16000:
            audio = audio.set_frame_rate(16000)
            print(f"采样率已调整为16kHz (原始: {audio.frame_rate}Hz)")
        
        # 导出为WAV格式
        audio.export(output_file, format="wav")
        print(f"音频已成功转换并保存到: {output_file}")
        
        # 显示音频信息
        duration = len(audio) / 1000.0  # 转换为秒
        print(f"音频时长: {duration:.2f}秒")
        print(f"采样率: {audio.frame_rate}Hz")
        print(f"声道数: {audio.channels}")
        
        return True
        
    except Exception as e:
        print(f"转换失败: {e}")
        return False

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='简化的音频转换工具')
    parser.add_argument('input_file', help='输入音频文件')
    parser.add_argument('output_file', help='输出WAV文件')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.input_file):
        print(f"错误: 输入文件不存在: {args.input_file}")
        sys.exit(1)
    
    success = convert_audio_to_wav(args.input_file, args.output_file)
    if not success:
        sys.exit(1)
