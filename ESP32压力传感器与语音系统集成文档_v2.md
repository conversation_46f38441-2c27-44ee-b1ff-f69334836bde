# ESP32压力传感器与语音系统集成开发文档

## 项目概述

本文档详细说明如何在现有ESP32语音系统中添加压力传感器功能。当传感器被按压时，系统将模拟语音输入，向DeepSeek云端大模型发送随机指令，并通过现有的语音合成系统播放AI回应。

## 系统架构

### 数据流程
```
压力传感器 → ADC检测 → 模拟语音输入 → 现有语音处理流程 → DeepSeek API → 语音合成输出
```

### 核心思路
- **复用现有语音流程**: 不重新实现API调用，而是模拟麦克风输入
- **集成现有TTS**: 使用已配置的语音合成系统
- **最小化修改**: 在现有架构基础上添加压力传感器支持

## 硬件连接

### 压力传感器连接图
```
ESP32-S3           压力传感器(FSR402)
GPIO36 (ADC1_CH0) ←→ 传感器一端
3.3V              ←→ 10kΩ电阻 ←→ 传感器另一端  
GND               ←→ 10kΩ电阻另一端
```

## 代码实现

### 1. 压力传感器类定义 (main/pressure_sensor.h)

```cpp
#ifndef PRESSURE_SENSOR_H
#define PRESSURE_SENSOR_H

#include <esp_adc/adc_oneshot.h>
#include <esp_timer.h>
#include <functional>

/**
 * @brief 压力传感器类
 * 负责检测压力变化并触发回调函数
 */
class PressureSensor {
public:
    /**
     * @brief 构造函数
     * @param channel ADC通道 (默认ADC_CHANNEL_0对应GPIO36)
     * @param threshold 压力阈值 (0-4095，默认2000)
     * @param debounce_ms 防抖时间 (毫秒，默认200)
     */
    PressureSensor(adc_channel_t channel = ADC_CHANNEL_0, 
                   int threshold = 2000, 
                   int debounce_ms = 200);
    
    /**
     * @brief 析构函数
     */
    ~PressureSensor();
    
    /**
     * @brief 初始化压力传感器
     * @return ESP_OK 成功，其他值表示失败
     */
    esp_err_t Initialize();
    
    /**
     * @brief 设置压力检测回调函数
     * @param callback 当检测到压力时调用的回调函数，参数为压力值
     */
    void SetPressureCallback(std::function<void(int pressure_value)> callback);
    
    /**
     * @brief 开始压力监控
     */
    void StartMonitoring();
    
    /**
     * @brief 停止压力监控
     */
    void StopMonitoring();
    
    /**
     * @brief 获取当前压力值
     * @return 当前ADC读取的压力值 (0-4095)
     */
    int GetCurrentPressure();

private:
    adc_oneshot_unit_handle_t adc_handle_;      // ADC单元句柄
    adc_channel_t channel_;                     // ADC通道
    int pressure_threshold_;                    // 压力触发阈值
    int debounce_time_ms_;                     // 防抖时间
    bool is_monitoring_;                       // 是否正在监控
    bool last_pressed_state_;                  // 上次按压状态
    int64_t last_press_time_;                  // 上次按压时间
    
    std::function<void(int)> pressure_callback_;  // 压力检测回调函数
    esp_timer_handle_t monitor_timer_;            // 监控定时器
    
    /**
     * @brief 定时器回调函数 (静态函数)
     * @param arg 传递给回调的参数 (PressureSensor实例指针)
     */
    static void MonitorTimerCallback(void* arg);
    
    /**
     * @brief 处理压力检测逻辑
     */
    void ProcessPressureDetection();
};

#endif // PRESSURE_SENSOR_H
```

### 2. 压力传感器实现 (main/pressure_sensor.cc)

```cpp
#include "pressure_sensor.h"
#include <esp_log.h>
#include <esp_adc/adc_cali.h>
#include <esp_adc/adc_cali_scheme.h>

static const char* TAG = "PressureSensor";

PressureSensor::PressureSensor(adc_channel_t channel, int threshold, int debounce_ms)
    : adc_handle_(nullptr)
    , channel_(channel)
    , pressure_threshold_(threshold)
    , debounce_time_ms_(debounce_ms)
    , is_monitoring_(false)
    , last_pressed_state_(false)
    , last_press_time_(0)
    , monitor_timer_(nullptr) {
    ESP_LOGI(TAG, "PressureSensor created - Channel: %d, Threshold: %d, Debounce: %dms", 
             channel, threshold, debounce_ms);
}

PressureSensor::~PressureSensor() {
    // 停止监控并清理资源
    StopMonitoring();
    if (adc_handle_) {
        adc_oneshot_del_unit(adc_handle_);
        ESP_LOGI(TAG, "ADC unit deleted");
    }
}

esp_err_t PressureSensor::Initialize() {
    ESP_LOGI(TAG, "Initializing pressure sensor...");
    
    // 配置ADC单元
    adc_oneshot_unit_init_cfg_t init_config = {
        .unit_id = ADC_UNIT_1,                    // 使用ADC1单元
        .ulp_mode = ADC_ULP_MODE_DISABLE,         // 禁用ULP模式
    };
    
    esp_err_t ret = adc_oneshot_new_unit(&init_config, &adc_handle_);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize ADC unit: %s", esp_err_to_name(ret));
        return ret;
    }
    
    // 配置ADC通道
    adc_oneshot_chan_cfg_t config = {
        .atten = ADC_ATTEN_DB_11,                 // 11dB衰减，支持0-3.3V输入
        .bitwidth = ADC_BITWIDTH_12,              // 12位分辨率 (0-4095)
    };
    
    ret = adc_oneshot_config_channel(adc_handle_, channel_, &config);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to configure ADC channel %d: %s", channel_, esp_err_to_name(ret));
        return ret;
    }
    
    ESP_LOGI(TAG, "Pressure sensor initialized successfully on GPIO%d", 
             (channel_ == ADC_CHANNEL_0) ? 36 : channel_);
    return ESP_OK;
}

void PressureSensor::SetPressureCallback(std::function<void(int)> callback) {
    pressure_callback_ = callback;
    ESP_LOGI(TAG, "Pressure callback function set");
}

void PressureSensor::StartMonitoring() {
    if (is_monitoring_) {
        ESP_LOGW(TAG, "Monitoring already started");
        return;
    }
    
    ESP_LOGI(TAG, "Starting pressure monitoring...");
    
    // 创建定时器，每50ms检查一次压力状态
    esp_timer_create_args_t timer_args = {
        .callback = MonitorTimerCallback,         // 定时器回调函数
        .arg = this,                             // 传递当前实例指针
        .dispatch_method = ESP_TIMER_TASK,       // 在任务中执行回调
        .name = "pressure_monitor"               // 定时器名称
    };
    
    esp_err_t ret = esp_timer_create(&timer_args, &monitor_timer_);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to create monitor timer: %s", esp_err_to_name(ret));
        return;
    }
    
    // 启动周期性定时器，每50ms触发一次
    ret = esp_timer_start_periodic(monitor_timer_, 50000); // 50ms = 50000微秒
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to start monitor timer: %s", esp_err_to_name(ret));
        return;
    }
    
    is_monitoring_ = true;
    ESP_LOGI(TAG, "Pressure monitoring started (50ms interval)");
}

void PressureSensor::StopMonitoring() {
    if (!is_monitoring_) {
        return;
    }
    
    ESP_LOGI(TAG, "Stopping pressure monitoring...");
    
    // 停止并删除定时器
    if (monitor_timer_) {
        esp_timer_stop(monitor_timer_);
        esp_timer_delete(monitor_timer_);
        monitor_timer_ = nullptr;
    }
    
    is_monitoring_ = false;
    ESP_LOGI(TAG, "Pressure monitoring stopped");
}

int PressureSensor::GetCurrentPressure() {
    if (!adc_handle_) {
        ESP_LOGW(TAG, "ADC not initialized");
        return 0;
    }
    
    int adc_raw = 0;
    esp_err_t ret = adc_oneshot_read(adc_handle_, channel_, &adc_raw);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to read ADC: %s", esp_err_to_name(ret));
        return 0;
    }
    
    return adc_raw;
}

void PressureSensor::MonitorTimerCallback(void* arg) {
    // 静态回调函数，将调用转发给实例方法
    PressureSensor* sensor = static_cast<PressureSensor*>(arg);
    sensor->ProcessPressureDetection();
}

void PressureSensor::ProcessPressureDetection() {
    // 读取当前压力值
    int pressure_value = GetCurrentPressure();
    bool is_pressed = pressure_value > pressure_threshold_;
    int64_t current_time = esp_timer_get_time() / 1000; // 转换为毫秒
    
    // 防抖处理：只有状态改变且超过防抖时间才处理
    if (is_pressed != last_pressed_state_) {
        if (current_time - last_press_time_ > debounce_time_ms_) {
            last_pressed_state_ = is_pressed;
            last_press_time_ = current_time;
            
            // 只在按下时触发回调（不在释放时触发）
            if (is_pressed && pressure_callback_) {
                ESP_LOGI(TAG, "Pressure detected! Value: %d (threshold: %d)", 
                         pressure_value, pressure_threshold_);
                pressure_callback_(pressure_value);
            }
        }
    }
}
```

### 3. Application类集成 (main/application.h 修改)

在Application类中添加压力传感器支持：

```cpp
// 在application.h文件顶部添加包含
#include "pressure_sensor.h"
#include <vector>
#include <string>
#include <random>

class Application {
    // ... 现有的public方法 ...

public:
    /**
     * @brief 初始化压力传感器
     * 在Start()方法中调用
     */
    void InitializePressureSensor();

    /**
     * @brief 压力检测回调处理函数
     * @param pressure_value 检测到的压力值
     */
    void OnPressureDetected(int pressure_value);

private:
    // ... 现有的private成员 ...

    /**
     * @brief 压力传感器实例
     */
    std::unique_ptr<PressureSensor> pressure_sensor_;

    /**
     * @brief 随机数生成器
     */
    std::mt19937 random_generator_;

    /**
     * @brief 获取随机的压力触发消息
     * @return 随机选择的消息字符串
     */
    std::string GetRandomPressureMessage();

    /**
     * @brief 模拟语音输入，将文本作为语音命令发送给系统
     * @param text 要发送的文本命令
     */
    void SimulateVoiceInput(const std::string& text);
};
```

### 4. Application类实现 (main/application.cc 修改)

```cpp
// 在application.cc文件顶部添加
#include "pressure_sensor.h"

// 在Application构造函数中添加初始化
Application::Application() {
    // ... 现有的构造函数代码 ...

    // 初始化随机数生成器
    std::random_device rd;
    random_generator_.seed(rd());

    // 创建压力传感器实例
    // GPIO36, 阈值2000, 防抖200ms
    pressure_sensor_ = std::make_unique<PressureSensor>(ADC_CHANNEL_0, 2000, 200);

    ESP_LOGI(TAG, "Application initialized with pressure sensor support");
}

void Application::InitializePressureSensor() {
    ESP_LOGI(TAG, "Initializing pressure sensor system...");

    // 初始化压力传感器硬件
    esp_err_t ret = pressure_sensor_->Initialize();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize pressure sensor: %s", esp_err_to_name(ret));
        return;
    }

    // 设置压力检测回调函数
    // 使用lambda表达式捕获this指针，调用成员方法
    pressure_sensor_->SetPressureCallback([this](int pressure_value) {
        this->OnPressureDetected(pressure_value);
    });

    // 开始压力监控
    pressure_sensor_->StartMonitoring();

    ESP_LOGI(TAG, "Pressure sensor system initialized and monitoring started");
}

void Application::OnPressureDetected(int pressure_value) {
    ESP_LOGI(TAG, "=== PRESSURE DETECTED ===");
    ESP_LOGI(TAG, "Pressure value: %d", pressure_value);

    // 更新屏幕显示状态
    auto display = Board::GetInstance().GetDisplay();
    if (display) {
        display->SetStatus("压力触发");
        display->SetChatMessage("system", "检测到压力，正在发送AI指令...");
        display->SetEmotion("thinking");
        display->ShowNotification("压力传感器激活", 2000);
    }

    // 获取随机的压力触发消息
    std::string pressure_message = GetRandomPressureMessage();
    ESP_LOGI(TAG, "Generated pressure message: %s", pressure_message.c_str());

    // 模拟语音输入，将消息发送给现有的语音处理系统
    SimulateVoiceInput(pressure_message);
}

std::string Application::GetRandomPressureMessage() {
    // 定义多种压力触发消息，让AI有不同的回应
    static const std::vector<std::string> pressure_messages = {
        "我被按压了，请给我一个有趣的回应",
        "有人按了我，请说点什么让我开心的话",
        "我感受到了压力，请安慰我一下",
        "被按压的感觉真奇妙，请给我讲个笑话",
        "我被触摸了，请用温暖的话语回应我",
        "压力传感器被激活了，请给我一个惊喜回应",
        "我被轻轻按了一下，请跟我聊聊天",
        "感受到了你的触摸，请给我一些鼓励的话",
        "压力来了，请帮我放松一下心情",
        "被按压让我很兴奋，请说些有趣的事情"
    };

    // 使用随机数生成器选择消息
    std::uniform_int_distribution<size_t> dist(0, pressure_messages.size() - 1);
    size_t index = dist(random_generator_);

    return pressure_messages[index];
}

void Application::SimulateVoiceInput(const std::string& text) {
    ESP_LOGI(TAG, "Simulating voice input: %s", text.c_str());

    // 方法1: 如果系统有直接的文本处理接口
    // 直接调用现有的文本处理方法
    if (protocol_) {
        ESP_LOGI(TAG, "Sending text directly to protocol handler");

        // 更新设备状态为监听状态
        SetDeviceState(kDeviceStateListening);

        // 直接发送文本给协议处理器
        // 这里假设protocol_有处理文本输入的方法
        // 您需要根据实际的protocol_接口进行调整
        protocol_->SendTextMessage(text);

        return;
    }

    // 方法2: 如果需要通过语音识别流程
    // 模拟语音识别结果
    ESP_LOGI(TAG, "Simulating speech recognition result");

    // 设置设备状态
    SetDeviceState(kDeviceStateListening);

    // 模拟语音识别完成，直接触发文本处理
    // 这里需要根据您的具体语音处理流程进行调整
    Schedule([this, text]() {
        // 在主线程中处理
        ESP_LOGI(TAG, "Processing simulated voice input in main thread");

        // 如果有语音识别结果处理方法，直接调用
        // 例如: OnSpeechRecognitionResult(text);

        // 或者直接发送给服务器处理
        if (protocol_) {
            // 这里根据您的实际协议接口进行调整
            protocol_->SendMessage(text);
        }
    });
}

// 在Application::Start()方法中添加压力传感器初始化
void Application::Start() {
    // ... 现有的启动代码 ...

    // 在设备完全启动后初始化压力传感器
    ESP_LOGI(TAG, "Starting pressure sensor initialization...");
    InitializePressureSensor();

    // ... 继续现有的启动代码 ...
}
```

### 5. 协议接口适配 (根据实际情况调整)

如果您的系统使用特定的协议接口，需要添加文本消息发送方法：

```cpp
// 在protocol.h中添加 (如果不存在的话)
class Protocol {
public:
    /**
     * @brief 发送文本消息到云端
     * @param text 要发送的文本内容
     */
    virtual void SendTextMessage(const std::string& text) = 0;

    /**
     * @brief 发送普通消息 (现有方法的重载)
     * @param message 消息内容
     */
    virtual void SendMessage(const std::string& message) = 0;
};

// 在具体的协议实现类中添加
void YourProtocolImpl::SendTextMessage(const std::string& text) {
    ESP_LOGI(TAG, "Sending text message to cloud: %s", text.c_str());

    // 构建消息包
    // 这里需要根据您的实际协议格式进行调整
    // 例如构建JSON、protobuf或其他格式的消息

    // 发送到云端服务器
    // 使用现有的网络发送机制
}
```

### 6. 编译配置修改

#### CMakeLists.txt 修改
在 `main/CMakeLists.txt` 中添加新的源文件：

```cmake
idf_component_register(
    SRCS
        "application.cc"
        "pressure_sensor.cc"        # 添加压力传感器源文件
        # ... 其他现有源文件
    INCLUDE_DIRS "."
    REQUIRES
        esp_adc                     # 添加ADC依赖
        esp_timer                   # 添加定时器依赖
        # ... 其他现有依赖
)
```

#### Kconfig 配置添加
在 `main/Kconfig.projbuild` 中添加配置选项：

```
menu "Pressure Sensor Configuration"
    config PRESSURE_SENSOR_ENABLE
        bool "Enable Pressure Sensor"
        default y
        help
            Enable pressure sensor functionality

    config PRESSURE_SENSOR_GPIO
        int "Pressure Sensor GPIO Pin"
        default 36
        range 0 39
        depends on PRESSURE_SENSOR_ENABLE
        help
            GPIO pin for pressure sensor (ADC capable pin)

    config PRESSURE_SENSOR_THRESHOLD
        int "Pressure Detection Threshold"
        default 2000
        range 100 4000
        depends on PRESSURE_SENSOR_ENABLE
        help
            ADC threshold value for pressure detection (0-4095)

    config PRESSURE_SENSOR_DEBOUNCE_MS
        int "Pressure Sensor Debounce Time (ms)"
        default 200
        range 50 1000
        depends on PRESSURE_SENSOR_ENABLE
        help
            Debounce time in milliseconds to avoid false triggers
endmenu
```

## 使用说明

### 1. 硬件安装步骤
1. **准备材料**: FSR402压力传感器、10kΩ电阻、面包板、杜邦线
2. **连接电路**: 按照连接图将传感器连接到GPIO36
3. **测试连接**: 使用万用表检查连接是否正确
4. **固定传感器**: 将传感器固定在合适的位置

### 2. 软件配置步骤
1. **添加源文件**: 将pressure_sensor.h和pressure_sensor.cc添加到项目
2. **修改Application**: 按照文档修改application.h和application.cc
3. **更新编译配置**: 修改CMakeLists.txt和Kconfig
4. **配置参数**: 根据需要调整阈值和防抖时间
5. **编译烧录**: 编译项目并烧录到设备

### 3. 功能测试
1. **启动检查**: 查看串口日志确认压力传感器初始化成功
2. **压力测试**: 按压传感器，观察日志输出
3. **语音测试**: 确认AI回应是否正常播放
4. **阈值调整**: 根据实际使用情况调整压力阈值

### 4. 调试技巧
```cpp
// 在ProcessPressureDetection()中添加调试日志
void PressureSensor::ProcessPressureDetection() {
    int pressure_value = GetCurrentPressure();

    // 添加详细的调试信息
    ESP_LOGD(TAG, "Current pressure: %d, threshold: %d, pressed: %s",
             pressure_value, pressure_threshold_,
             (pressure_value > pressure_threshold_) ? "YES" : "NO");

    // ... 其余代码保持不变
}
```

## 故障排除

### 1. 传感器无响应
- **检查硬件连接**: 确认GPIO36连接正确
- **检查电源**: 确认3.3V和GND连接稳定
- **调整阈值**: 降低压力阈值进行测试
- **查看日志**: 检查ADC读取值是否正常

### 2. 误触发问题
- **增加防抖时间**: 将debounce_ms设置为更大值
- **提高阈值**: 增加pressure_threshold值
- **检查环境干扰**: 确认没有电磁干扰

### 3. 语音系统集成问题
- **检查协议接口**: 确认protocol_接口方法正确
- **验证消息格式**: 检查发送的消息格式是否符合要求
- **测试网络连接**: 确认设备网络连接正常

这份重新编写的文档更加符合您现有的语音系统架构，通过模拟语音输入的方式来触发现有的语音处理流程，避免了重复实现API调用和TTS功能。所有代码都添加了详细的中文注释，便于理解和维护。
