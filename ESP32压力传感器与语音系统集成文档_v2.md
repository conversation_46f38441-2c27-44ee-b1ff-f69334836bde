# ESP32压力传感器与语音系统集成开发文档

## 项目概述

本文档详细说明如何在现有ESP32语音系统中添加压力传感器功能。通过深入分析现有的麦克风语音处理流程，我们将创建一个独立的压力传感器处理系统，该系统能够生成文字消息并通过现有的协议接口发送给DeepSeek云端大模型，同时确保与麦克风功能的协调工作，避免冲突并实现有序的交互体验。

## 系统架构

### 现有麦克风语音处理流程分析

通过分析Application类的代码，我们发现现有的语音处理流程如下：

```
1. 唤醒词检测 → wake_word_->OnWakeWordDetected()
   ↓
2. 打开音频通道 → protocol_->OpenAudioChannel()
   ↓
3. 开始监听模式 → SetListeningMode() → SetDeviceState(kDeviceStateListening)
   ↓
4. 音频处理器启动 → audio_processor_->Start()
   ↓
5. 发送开始监听命令 → protocol_->SendStartListening()
   ↓
6. 音频数据编码发送 → opus_encoder_->Encode() → protocol_->SendAudio()
   ↓
7. 接收云端JSON响应 → protocol_->OnIncomingJson()
   ↓
8. 处理STT结果 → type="stt" → 显示用户说的话
   ↓
9. 处理TTS开始 → type="tts", state="start" → SetDeviceState(kDeviceStateSpeaking)
   ↓
10. 接收音频数据 → protocol_->OnIncomingAudio() → 播放AI回复
    ↓
11. TTS结束 → type="tts", state="stop" → 返回监听或空闲状态
```

### 压力传感器独立处理流程设计

基于对现有流程的理解，我们设计独立的压力传感器处理流程：

```
1. 压力检测 → PressureSensor::OnPressureDetected()
   ↓
2. 检查设备状态 → 确保不与麦克风冲突
   ↓
3. 生成文字消息 → GetRandomPressureMessage()
   ↓
4. 打开音频通道 → protocol_->OpenAudioChannel() (如果未打开)
   ↓
5. 发送文字消息 → SendPressureTextMessage() (独立函数)
   ↓
6. 等待云端响应 → 复用现有的OnIncomingJson处理
   ↓
7. 播放AI回复 → 复用现有的TTS播放机制
   ↓
8. 恢复原始状态 → 确保不影响后续麦克风使用
```

### 核心设计原则
- **独立处理函数**: 压力传感器有自己的处理函数，不复用麦克风的函数
- **状态协调机制**: 通过设备状态管理确保麦克风和压力传感器有序工作
- **复用协议接口**: 使用相同的protocol_接口发送消息和接收响应
- **复用TTS播放**: 使用相同的OnIncomingJson和音频播放机制

## 硬件连接

### 压力传感器连接图
```
ESP32-S3           压力传感器(FSR402)
GPIO36 (ADC1_CH0) ←→ 传感器一端
3.3V              ←→ 10kΩ电阻 ←→ 传感器另一端  
GND               ←→ 10kΩ电阻另一端
```

## 代码实现

### 1. 压力传感器类定义 (main/pressure_sensor.h)

```cpp
#ifndef PRESSURE_SENSOR_H
#define PRESSURE_SENSOR_H

#include <esp_adc/adc_oneshot.h>
#include <esp_timer.h>
#include <functional>

/**
 * @brief 压力传感器类
 * 负责检测压力变化并触发回调函数
 */
class PressureSensor {
public:
    /**
     * @brief 构造函数
     * @param channel ADC通道 (默认ADC_CHANNEL_0对应GPIO36)
     * @param threshold 压力阈值 (0-4095，默认2000)
     * @param debounce_ms 防抖时间 (毫秒，默认200)
     */
    PressureSensor(adc_channel_t channel = ADC_CHANNEL_0, 
                   int threshold = 2000, 
                   int debounce_ms = 200);
    
    /**
     * @brief 析构函数
     */
    ~PressureSensor();
    
    /**
     * @brief 初始化压力传感器
     * @return ESP_OK 成功，其他值表示失败
     */
    esp_err_t Initialize();
    
    /**
     * @brief 设置压力检测回调函数
     * @param callback 当检测到压力时调用的回调函数，参数为压力值
     */
    void SetPressureCallback(std::function<void(int pressure_value)> callback);
    
    /**
     * @brief 开始压力监控
     */
    void StartMonitoring();
    
    /**
     * @brief 停止压力监控
     */
    void StopMonitoring();
    
    /**
     * @brief 获取当前压力值
     * @return 当前ADC读取的压力值 (0-4095)
     */
    int GetCurrentPressure();

private:
    adc_oneshot_unit_handle_t adc_handle_;      // ADC单元句柄
    adc_channel_t channel_;                     // ADC通道
    int pressure_threshold_;                    // 压力触发阈值
    int debounce_time_ms_;                     // 防抖时间
    bool is_monitoring_;                       // 是否正在监控
    bool last_pressed_state_;                  // 上次按压状态
    int64_t last_press_time_;                  // 上次按压时间
    
    std::function<void(int)> pressure_callback_;  // 压力检测回调函数
    esp_timer_handle_t monitor_timer_;            // 监控定时器
    
    /**
     * @brief 定时器回调函数 (静态函数)
     * @param arg 传递给回调的参数 (PressureSensor实例指针)
     */
    static void MonitorTimerCallback(void* arg);
    
    /**
     * @brief 处理压力检测逻辑
     */
    void ProcessPressureDetection();
};

#endif // PRESSURE_SENSOR_H
```

### 2. 压力传感器实现 (main/pressure_sensor.cc)

```cpp
#include "pressure_sensor.h"
#include <esp_log.h>
#include <esp_adc/adc_cali.h>
#include <esp_adc/adc_cali_scheme.h>

static const char* TAG = "PressureSensor";

PressureSensor::PressureSensor(adc_channel_t channel, int threshold, int debounce_ms)
    : adc_handle_(nullptr)
    , channel_(channel)
    , pressure_threshold_(threshold)
    , debounce_time_ms_(debounce_ms)
    , is_monitoring_(false)
    , last_pressed_state_(false)
    , last_press_time_(0)
    , monitor_timer_(nullptr) {
    ESP_LOGI(TAG, "PressureSensor created - Channel: %d, Threshold: %d, Debounce: %dms", 
             channel, threshold, debounce_ms);
}

PressureSensor::~PressureSensor() {
    // 停止监控并清理资源
    StopMonitoring();
    if (adc_handle_) {
        adc_oneshot_del_unit(adc_handle_);
        ESP_LOGI(TAG, "ADC unit deleted");
    }
}

esp_err_t PressureSensor::Initialize() {
    ESP_LOGI(TAG, "Initializing pressure sensor...");
    
    // 配置ADC单元
    adc_oneshot_unit_init_cfg_t init_config = {
        .unit_id = ADC_UNIT_1,                    // 使用ADC1单元
        .ulp_mode = ADC_ULP_MODE_DISABLE,         // 禁用ULP模式
    };
    
    esp_err_t ret = adc_oneshot_new_unit(&init_config, &adc_handle_);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize ADC unit: %s", esp_err_to_name(ret));
        return ret;
    }
    
    // 配置ADC通道
    adc_oneshot_chan_cfg_t config = {
        .atten = ADC_ATTEN_DB_11,                 // 11dB衰减，支持0-3.3V输入
        .bitwidth = ADC_BITWIDTH_12,              // 12位分辨率 (0-4095)
    };
    
    ret = adc_oneshot_config_channel(adc_handle_, channel_, &config);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to configure ADC channel %d: %s", channel_, esp_err_to_name(ret));
        return ret;
    }
    
    ESP_LOGI(TAG, "Pressure sensor initialized successfully on GPIO%d", 
             (channel_ == ADC_CHANNEL_0) ? 36 : channel_);
    return ESP_OK;
}

void PressureSensor::SetPressureCallback(std::function<void(int)> callback) {
    pressure_callback_ = callback;
    ESP_LOGI(TAG, "Pressure callback function set");
}

void PressureSensor::StartMonitoring() {
    if (is_monitoring_) {
        ESP_LOGW(TAG, "Monitoring already started");
        return;
    }
    
    ESP_LOGI(TAG, "Starting pressure monitoring...");
    
    // 创建定时器，每50ms检查一次压力状态
    esp_timer_create_args_t timer_args = {
        .callback = MonitorTimerCallback,         // 定时器回调函数
        .arg = this,                             // 传递当前实例指针
        .dispatch_method = ESP_TIMER_TASK,       // 在任务中执行回调
        .name = "pressure_monitor"               // 定时器名称
    };
    
    esp_err_t ret = esp_timer_create(&timer_args, &monitor_timer_);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to create monitor timer: %s", esp_err_to_name(ret));
        return;
    }
    
    // 启动周期性定时器，每50ms触发一次
    ret = esp_timer_start_periodic(monitor_timer_, 50000); // 50ms = 50000微秒
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to start monitor timer: %s", esp_err_to_name(ret));
        return;
    }
    
    is_monitoring_ = true;
    ESP_LOGI(TAG, "Pressure monitoring started (50ms interval)");
}

void PressureSensor::StopMonitoring() {
    if (!is_monitoring_) {
        return;
    }
    
    ESP_LOGI(TAG, "Stopping pressure monitoring...");
    
    // 停止并删除定时器
    if (monitor_timer_) {
        esp_timer_stop(monitor_timer_);
        esp_timer_delete(monitor_timer_);
        monitor_timer_ = nullptr;
    }
    
    is_monitoring_ = false;
    ESP_LOGI(TAG, "Pressure monitoring stopped");
}

int PressureSensor::GetCurrentPressure() {
    if (!adc_handle_) {
        ESP_LOGW(TAG, "ADC not initialized");
        return 0;
    }
    
    int adc_raw = 0;
    esp_err_t ret = adc_oneshot_read(adc_handle_, channel_, &adc_raw);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to read ADC: %s", esp_err_to_name(ret));
        return 0;
    }
    
    return adc_raw;
}

void PressureSensor::MonitorTimerCallback(void* arg) {
    // 静态回调函数，将调用转发给实例方法
    PressureSensor* sensor = static_cast<PressureSensor*>(arg);
    sensor->ProcessPressureDetection();
}

void PressureSensor::ProcessPressureDetection() {
    // 读取当前压力值
    int pressure_value = GetCurrentPressure();
    bool is_pressed = pressure_value > pressure_threshold_;
    int64_t current_time = esp_timer_get_time() / 1000; // 转换为毫秒
    
    // 防抖处理：只有状态改变且超过防抖时间才处理
    if (is_pressed != last_pressed_state_) {
        if (current_time - last_press_time_ > debounce_time_ms_) {
            last_pressed_state_ = is_pressed;
            last_press_time_ = current_time;
            
            // 只在按下时触发回调（不在释放时触发）
            if (is_pressed && pressure_callback_) {
                ESP_LOGI(TAG, "Pressure detected! Value: %d (threshold: %d)", 
                         pressure_value, pressure_threshold_);
                pressure_callback_(pressure_value);
            }
        }
    }
}
```

### 3. Application类集成 (main/application.h 修改)

在Application类中添加压力传感器支持：

```cpp
// 在application.h文件顶部添加包含
#include "pressure_sensor.h"
#include <vector>
#include <string>
#include <random>

class Application {
    // ... 现有的public方法 ...

public:
    /**
     * @brief 初始化压力传感器
     * 在Start()方法中调用
     */
    void InitializePressureSensor();

    /**
     * @brief 压力检测回调处理函数
     * @param pressure_value 检测到的压力值
     */
    void OnPressureDetected(int pressure_value);

    /**
     * @brief 发送压力传感器触发的文字消息到云端
     * @param text 要发送的文字消息
     * @return true 发送成功，false 发送失败
     */
    bool SendPressureTextMessage(const std::string& text);

    /**
     * @brief 检查当前是否可以处理压力传感器输入
     * @return true 可以处理，false 当前忙碌不能处理
     */
    bool CanHandlePressureInput() const;

private:
    // ... 现有的private成员 ...

    /**
     * @brief 压力传感器实例
     */
    std::unique_ptr<PressureSensor> pressure_sensor_;

    /**
     * @brief 随机数生成器
     */
    std::mt19937 random_generator_;

    /**
     * @brief 获取随机的压力触发消息
     * @return 随机选择的消息字符串
     */
    std::string GetRandomPressureMessage();

private:
    /**
     * @brief 压力传感器状态标记
     * 用于跟踪压力传感器是否正在处理请求，避免与麦克风冲突
     */
    bool pressure_sensor_processing_;

    /**
     * @brief 压力传感器会话ID
     * 用于跟踪压力传感器触发的会话，确保响应正确匹配
     */
    std::string pressure_session_id_;
};
```

### 4. Application类实现 (main/application.cc 修改)

```cpp
// 在application.cc文件顶部添加
#include "pressure_sensor.h"

// 在Application构造函数中添加初始化
Application::Application()
    : pressure_sensor_processing_(false)  // 初始化压力传感器处理状态
    , pressure_session_id_("")           // 初始化会话ID
{
    // ... 现有的构造函数代码 ...

    // 初始化随机数生成器
    std::random_device rd;
    random_generator_.seed(rd());

    // 创建压力传感器实例
    // GPIO36 (ADC_CHANNEL_0), 压力阈值2000, 防抖时间200ms
    pressure_sensor_ = std::make_unique<PressureSensor>(ADC_CHANNEL_0, 2000, 200);

    ESP_LOGI(TAG, "Application initialized with pressure sensor support");
    ESP_LOGI(TAG, "Pressure sensor: GPIO36, threshold=2000, debounce=200ms");
}

void Application::InitializePressureSensor() {
    ESP_LOGI(TAG, "Initializing pressure sensor system...");

    // 初始化压力传感器硬件
    esp_err_t ret = pressure_sensor_->Initialize();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize pressure sensor: %s", esp_err_to_name(ret));
        return;
    }

    // 设置压力检测回调函数
    // 使用lambda表达式捕获this指针，调用成员方法
    pressure_sensor_->SetPressureCallback([this](int pressure_value) {
        this->OnPressureDetected(pressure_value);
    });

    // 开始压力监控
    pressure_sensor_->StartMonitoring();

    ESP_LOGI(TAG, "Pressure sensor system initialized and monitoring started");
}

void Application::OnPressureDetected(int pressure_value) {
    ESP_LOGI(TAG, "=== PRESSURE SENSOR TRIGGERED ===");
    ESP_LOGI(TAG, "Detected pressure value: %d", pressure_value);

    // 检查当前是否可以处理压力传感器输入
    if (!CanHandlePressureInput()) {
        ESP_LOGW(TAG, "Cannot handle pressure input now, device is busy");
        ESP_LOGW(TAG, "Current device state: %s", STATE_STRINGS[device_state_]);

        // 显示忙碌提示
        auto display = Board::GetInstance().GetDisplay();
        if (display) {
            display->ShowNotification("设备忙碌中，请稍后再试", 2000);
        }
        return;
    }

    // 标记压力传感器正在处理
    pressure_sensor_processing_ = true;

    // 更新屏幕显示状态
    auto display = Board::GetInstance().GetDisplay();
    if (display) {
        display->SetStatus("压力触发");
        display->SetChatMessage("system", "检测到压力，正在向AI发送消息...");
        display->SetEmotion("thinking");
        display->ShowNotification("压力传感器激活", 2000);
    }

    // 生成随机的压力触发消息
    std::string pressure_message = GetRandomPressureMessage();
    ESP_LOGI(TAG, "Generated pressure message: %s", pressure_message.c_str());

    // 使用独立的压力传感器处理函数发送消息
    bool success = SendPressureTextMessage(pressure_message);
    if (!success) {
        ESP_LOGE(TAG, "Failed to send pressure message");
        pressure_sensor_processing_ = false;

        // 显示错误提示
        if (display) {
            display->SetStatus("发送失败");
            display->SetChatMessage("system", "消息发送失败，请检查网络连接");
            display->SetEmotion("sad");
        }
    }
}

bool Application::CanHandlePressureInput() const {
    /**
     * 检查当前设备状态是否允许处理压力传感器输入
     * 避免与麦克风语音处理产生冲突
     */

    // 如果压力传感器正在处理中，不允许新的输入
    if (pressure_sensor_processing_) {
        ESP_LOGD(TAG, "Pressure sensor is already processing");
        return false;
    }

    // 检查设备状态，只有在空闲状态才允许压力传感器输入
    switch (device_state_) {
        case kDeviceStateIdle:
            // 空闲状态，可以处理压力输入
            return true;

        case kDeviceStateListening:
        case kDeviceStateSpeaking:
        case kDeviceStateConnecting:
            // 正在进行语音交互，不允许压力输入干扰
            ESP_LOGD(TAG, "Device is in voice interaction state: %s", STATE_STRINGS[device_state_]);
            return false;

        case kDeviceStateStarting:
        case kDeviceStateWifiConfiguring:
        case kDeviceStateUpgrading:
        case kDeviceStateActivating:
        case kDeviceStateAudioTesting:
        case kDeviceStateFatalError:
            // 设备正在初始化或有错误，不允许压力输入
            ESP_LOGD(TAG, "Device is not ready for pressure input: %s", STATE_STRINGS[device_state_]);
            return false;

        default:
            return false;
    }
}

bool Application::SendPressureTextMessage(const std::string& text) {
    /**
     * 独立的压力传感器消息发送函数
     * 不复用麦克风的处理流程，但使用相同的协议接口
     */
    ESP_LOGI(TAG, "=== SENDING PRESSURE MESSAGE ===");
    ESP_LOGI(TAG, "Message: %s", text.c_str());

    // 检查协议是否可用
    if (!protocol_) {
        ESP_LOGE(TAG, "Protocol not initialized");
        return false;
    }

    // 确保音频通道已打开（如果未打开则打开）
    if (!protocol_->IsAudioChannelOpened()) {
        ESP_LOGI(TAG, "Opening audio channel for pressure message");
        SetDeviceState(kDeviceStateConnecting);

        if (!protocol_->OpenAudioChannel()) {
            ESP_LOGE(TAG, "Failed to open audio channel");
            SetDeviceState(kDeviceStateIdle);
            return false;
        }

        ESP_LOGI(TAG, "Audio channel opened successfully");
    }

    // 生成压力传感器专用的会话ID
    pressure_session_id_ = "pressure_" + std::to_string(esp_timer_get_time() / 1000);
    ESP_LOGI(TAG, "Generated pressure session ID: %s", pressure_session_id_.c_str());

    // 构建JSON消息（参考现有的SendWakeWordDetected格式）
    std::string json_message = "{";
    json_message += "\"session_id\":\"" + pressure_session_id_ + "\",";
    json_message += "\"type\":\"pressure_text\",";  // 使用特殊的消息类型标识
    json_message += "\"text\":\"" + text + "\",";
    json_message += "\"source\":\"pressure_sensor\"";  // 标识消息来源
    json_message += "}";

    ESP_LOGI(TAG, "Sending JSON: %s", json_message.c_str());

    // 发送消息到云端
    bool success = protocol_->SendText(json_message);
    if (success) {
        ESP_LOGI(TAG, "Pressure message sent successfully");

        // 设置设备状态为监听（等待响应）
        SetDeviceState(kDeviceStateListening);

        // 更新屏幕显示
        auto display = Board::GetInstance().GetDisplay();
        if (display) {
            display->SetStatus("等待AI回应");
            display->SetChatMessage("user", text.c_str());  // 显示发送的消息
            display->SetEmotion("thinking");
        }
    } else {
        ESP_LOGE(TAG, "Failed to send pressure message");
        pressure_sensor_processing_ = false;
        SetDeviceState(kDeviceStateIdle);
    }

    return success;
}

std::string Application::GetRandomPressureMessage() {
    /**
     * 生成随机的压力触发消息
     * 提供多样化的AI交互体验
     */
    static const std::vector<std::string> pressure_messages = {
        "我被按压了，请给我一个有趣的回应",
        "有人按了我，请说点什么让我开心的话",
        "我感受到了压力，请安慰我一下",
        "被按压的感觉真奇妙，请给我讲个笑话",
        "我被触摸了，请用温暖的话语回应我",
        "压力传感器被激活了，请给我一个惊喜回应",
        "我被轻轻按了一下，请跟我聊聊天",
        "感受到了你的触摸，请给我一些鼓励的话",
        "压力来了，请帮我放松一下心情",
        "被按压让我很兴奋，请说些有趣的事情",
        "我感受到了你的关注，请和我互动一下",
        "压力传感器告诉我有人在关心我，请回应我"
    };

    // 使用随机数生成器选择消息
    std::uniform_int_distribution<size_t> dist(0, pressure_messages.size() - 1);
    size_t index = dist(random_generator_);

    ESP_LOGD(TAG, "Selected message index: %zu/%zu", index, pressure_messages.size());
    return pressure_messages[index];
}

// 需要修改现有的OnIncomingJson处理函数，添加对压力传感器响应的处理
// 在Application::Start()中的protocol_->OnIncomingJson回调中添加以下代码：

/*
在现有的OnIncomingJson回调函数中添加压力传感器响应处理：

protocol_->OnIncomingJson([this, display](const cJSON* root) {
    // ... 现有的处理代码 ...

    // 添加压力传感器响应处理
    if (strcmp(type->valuestring, "tts") == 0) {
        auto state = cJSON_GetObjectItem(root, "state");

        if (strcmp(state->valuestring, "start") == 0) {
            Schedule([this]() {
                aborted_ = false;
                if (device_state_ == kDeviceStateIdle || device_state_ == kDeviceStateListening) {
                    SetDeviceState(kDeviceStateSpeaking);
                }
            });
        } else if (strcmp(state->valuestring, "stop") == 0) {
            Schedule([this]() {
                background_task_->WaitForCompletion();
                if (device_state_ == kDeviceStateSpeaking) {
                    // 检查是否是压力传感器触发的会话
                    if (pressure_sensor_processing_) {
                        ESP_LOGI(TAG, "Pressure sensor TTS completed");
                        pressure_sensor_processing_ = false;
                        pressure_session_id_ = "";

                        // 压力传感器会话结束，返回空闲状态
                        SetDeviceState(kDeviceStateIdle);

                        // 显示完成状态
                        auto display = Board::GetInstance().GetDisplay();
                        if (display) {
                            display->SetStatus("压力响应完成");
                            display->SetEmotion("happy");
                        }
                    } else {
                        // 正常的麦克风会话处理
                        if (listening_mode_ == kListeningModeManualStop) {
                            SetDeviceState(kDeviceStateIdle);
                        } else {
                            SetDeviceState(kDeviceStateListening);
                        }
                    }
                }
            });
        } else if (strcmp(state->valuestring, "sentence_start") == 0) {
            auto text = cJSON_GetObjectItem(root, "text");
            if (cJSON_IsString(text)) {
                ESP_LOGI(TAG, "<< %s", text->valuestring);
                Schedule([this, display, message = std::string(text->valuestring)]() {
                    display->SetChatMessage("assistant", message.c_str());

                    // 如果是压力传感器触发的响应，添加特殊标识
                    if (pressure_sensor_processing_) {
                        display->ShowNotification("压力传感器AI回应", 1000);
                    }
                });
            }
        }
    }

    // ... 其他现有的处理代码 ...
});
*/

// 辅助函数实现
bool Application::HasSpeechRecognitionHandler() {
    // 检查您的系统是否有专门的语音识别结果处理函数
    // 这里需要根据您的实际系统进行判断
    // 例如：return (speech_handler_ != nullptr);
    return false; // 默认返回false，请根据实际情况修改
}

void Application::OnSpeechRecognitionResult(const std::string& text) {
    /**
     * 这个函数应该已经存在于您的系统中，用于处理麦克风语音识别的结果
     * 如果不存在，您需要根据现有的语音处理流程来实现
     */
    ESP_LOGI(TAG, "Processing speech recognition result: %s", text.c_str());

    // 通常这个函数会做以下事情：
    // 1. 更新UI显示用户说的话
    // 2. 发送文字给云端大模型
    // 3. 等待大模型回复
    // 4. 使用TTS播放回复

    // 如果您的系统有这个函数，直接调用即可
    // 如果没有，请参考ProcessTextMessage的实现
    ProcessTextMessage(text);
}

void Application::ProcessTextMessage(const std::string& text) {
    /**
     * 通用的文字消息处理函数
     * 这里实现将文字发送给大模型并处理回复的完整流程
     */
    ESP_LOGI(TAG, "Processing text message: %s", text.c_str());

    // 更新屏幕显示
    auto display = Board::GetInstance().GetDisplay();
    if (display) {
        display->SetStatus("处理中");
        display->SetChatMessage("user", text.c_str());
        display->SetEmotion("thinking");
    }

    // 发送给云端大模型（使用现有的协议接口）
    if (protocol_) {
        // 这里调用您现有的发送消息接口
        // 这个接口应该会：
        // 1. 发送文字给DeepSeek
        // 2. 接收回复
        // 3. 触发TTS播放
        protocol_->SendMessage(text);
    } else {
        ESP_LOGE(TAG, "Protocol not available, cannot send message");
    }
}

// 在Application::Start()方法中添加压力传感器初始化
void Application::Start() {
    // ... 现有的启动代码 ...

    // 在设备完全启动后初始化压力传感器
    ESP_LOGI(TAG, "Starting pressure sensor initialization...");
    InitializePressureSensor();

    // ... 继续现有的启动代码 ...
}
```

### 5. 协议接口适配 (根据实际情况调整)

如果您的系统使用特定的协议接口，需要添加文本消息发送方法：

```cpp
// 在protocol.h中添加 (如果不存在的话)
class Protocol {
public:
    /**
     * @brief 发送文本消息到云端
     * @param text 要发送的文本内容
     */
    virtual void SendTextMessage(const std::string& text) = 0;

    /**
     * @brief 发送普通消息 (现有方法的重载)
     * @param message 消息内容
     */
    virtual void SendMessage(const std::string& message) = 0;
};

// 在具体的协议实现类中添加
void YourProtocolImpl::SendTextMessage(const std::string& text) {
    ESP_LOGI(TAG, "Sending text message to cloud: %s", text.c_str());

    // 构建消息包
    // 这里需要根据您的实际协议格式进行调整
    // 例如构建JSON、protobuf或其他格式的消息

    // 发送到云端服务器
    // 使用现有的网络发送机制
}
```

### 6. 编译配置修改

#### CMakeLists.txt 修改
在 `main/CMakeLists.txt` 中添加新的源文件：

```cmake
idf_component_register(
    SRCS
        "application.cc"
        "pressure_sensor.cc"        # 添加压力传感器源文件
        # ... 其他现有源文件
    INCLUDE_DIRS "."
    REQUIRES
        esp_adc                     # 添加ADC依赖
        esp_timer                   # 添加定时器依赖
        # ... 其他现有依赖
)
```

#### Kconfig 配置添加
在 `main/Kconfig.projbuild` 中添加配置选项：

```
menu "Pressure Sensor Configuration"
    config PRESSURE_SENSOR_ENABLE
        bool "Enable Pressure Sensor"
        default y
        help
            Enable pressure sensor functionality

    config PRESSURE_SENSOR_GPIO
        int "Pressure Sensor GPIO Pin"
        default 36
        range 0 39
        depends on PRESSURE_SENSOR_ENABLE
        help
            GPIO pin for pressure sensor (ADC capable pin)

    config PRESSURE_SENSOR_THRESHOLD
        int "Pressure Detection Threshold"
        default 2000
        range 100 4000
        depends on PRESSURE_SENSOR_ENABLE
        help
            ADC threshold value for pressure detection (0-4095)

    config PRESSURE_SENSOR_DEBOUNCE_MS
        int "Pressure Sensor Debounce Time (ms)"
        default 200
        range 50 1000
        depends on PRESSURE_SENSOR_ENABLE
        help
            Debounce time in milliseconds to avoid false triggers
endmenu
```

## 使用说明

### 1. 硬件安装步骤
1. **准备材料**: FSR402压力传感器、10kΩ电阻、面包板、杜邦线
2. **连接电路**: 按照连接图将传感器连接到GPIO36
3. **测试连接**: 使用万用表检查连接是否正确
4. **固定传感器**: 将传感器固定在合适的位置

### 2. 软件配置步骤
1. **添加源文件**: 将pressure_sensor.h和pressure_sensor.cc添加到项目
2. **修改Application**: 按照文档修改application.h和application.cc
3. **更新编译配置**: 修改CMakeLists.txt和Kconfig
4. **配置参数**: 根据需要调整阈值和防抖时间
5. **编译烧录**: 编译项目并烧录到设备

### 3. 功能测试
1. **启动检查**: 查看串口日志确认压力传感器初始化成功
2. **压力测试**: 按压传感器，观察日志输出
3. **语音测试**: 确认AI回应是否正常播放
4. **阈值调整**: 根据实际使用情况调整压力阈值

### 4. 调试技巧
```cpp
// 在ProcessPressureDetection()中添加调试日志
void PressureSensor::ProcessPressureDetection() {
    int pressure_value = GetCurrentPressure();

    // 添加详细的调试信息
    ESP_LOGD(TAG, "Current pressure: %d, threshold: %d, pressed: %s",
             pressure_value, pressure_threshold_,
             (pressure_value > pressure_threshold_) ? "YES" : "NO");

    // ... 其余代码保持不变
}
```

## 故障排除

### 1. 传感器无响应
- **检查硬件连接**: 确认GPIO36连接正确
- **检查电源**: 确认3.3V和GND连接稳定
- **调整阈值**: 降低压力阈值进行测试
- **查看日志**: 检查ADC读取值是否正常

### 2. 误触发问题
- **增加防抖时间**: 将debounce_ms设置为更大值
- **提高阈值**: 增加pressure_threshold值
- **检查环境干扰**: 确认没有电磁干扰

### 3. 语音系统集成问题
- **检查协议接口**: 确认protocol_接口方法正确
- **验证消息格式**: 检查发送的消息格式是否符合要求
- **测试网络连接**: 确认设备网络连接正常

## 完整使用流程说明

### 实际运行流程
```
1. 用户按压传感器
   ↓
2. ADC检测到压力变化
   ↓
3. 生成随机文字消息："我被按压了，请给我一个有趣的回应"
   ↓
4. 将文字消息作为"语音识别结果"输入到现有系统
   ↓
5. 现有系统处理文字消息：
   - 显示在屏幕上（就像显示用户说的话）
   - 发送给DeepSeek云端大模型
   - 等待AI回复
   ↓
6. 收到AI回复后：
   - 显示AI回复在屏幕上
   - 使用现有TTS系统播放语音
   ↓
7. 用户听到AI的语音回应
```

### 与现有麦克风功能的对比
| 步骤 | 麦克风流程 | 压力传感器流程 |
|------|------------|----------------|
| 1 | 用户说话 | 用户按压传感器 |
| 2 | 麦克风接收声音 | ADC检测压力 |
| 3 | 语音识别转换成文字 | 直接生成文字消息 |
| 4 | 发送文字给大模型 | 发送文字给大模型 |
| 5 | 接收AI回复 | 接收AI回复 |
| 6 | TTS播放回复 | TTS播放回复 |

**核心优势**: 压力传感器跳过了语音识别步骤，直接生成文字，但后续流程完全相同，实现了完美的系统集成。

这份文档提供了完整的压力传感器集成方案，通过模拟语音识别结果的方式，完美复用现有的语音处理、云端通信和TTS播放功能。所有代码都有详细的中文注释，便于理解和维护。
