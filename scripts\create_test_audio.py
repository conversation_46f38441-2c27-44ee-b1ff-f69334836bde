#!/usr/bin/env python3
# 创建测试音频文件
import numpy as np
import wave
import struct
import argparse

def create_test_audio(output_file, duration=5, frequency=440, sample_rate=16000):
    """创建一个测试音频文件（正弦波）"""
    try:
        # 生成正弦波
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        wave_data = np.sin(2 * np.pi * frequency * t)
        
        # 转换为16位整数
        wave_data = (wave_data * 32767).astype(np.int16)
        
        # 写入WAV文件
        with wave.open(output_file, 'wb') as wav_file:
            wav_file.setnchannels(1)  # 单声道
            wav_file.setsampwidth(2)  # 16位
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(wave_data.tobytes())
        
        print(f"测试音频已创建: {output_file}")
        print(f"时长: {duration}秒")
        print(f"频率: {frequency}Hz")
        print(f"采样率: {sample_rate}Hz")
        
        return True
        
    except Exception as e:
        print(f"创建测试音频失败: {e}")
        return False

def create_p3_format(wav_file, p3_file):
    """将WAV文件转换为简化的P3格式（仅用于测试）"""
    try:
        # 读取WAV文件
        with wave.open(wav_file, 'rb') as wav:
            frames = wav.readframes(wav.getnframes())
            sample_rate = wav.getframerate()
            channels = wav.getnchannels()
            
        print(f"读取WAV文件: {wav_file}")
        print(f"采样率: {sample_rate}Hz, 声道: {channels}")
        
        # 转换为16位整数数组
        audio_data = np.frombuffer(frames, dtype=np.int16)
        
        # 创建简化的P3格式（模拟opus包结构）
        with open(p3_file, 'wb') as f:
            # 每60ms一帧
            frame_duration_ms = 60
            frame_size = int(sample_rate * frame_duration_ms / 1000)
            
            for i in range(0, len(audio_data) - frame_size, frame_size):
                frame = audio_data[i:i + frame_size]
                # 简化的包头：版本(1字节) + 标志(1字节) + 数据长度(2字节) + 数据
                frame_bytes = frame.tobytes()
                packet_header = struct.pack('>BBH', 0, 0, len(frame_bytes))
                f.write(packet_header + frame_bytes)
        
        print(f"P3格式文件已创建: {p3_file}")
        return True
        
    except Exception as e:
        print(f"转换为P3格式失败: {e}")
        return False

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='创建测试音频文件')
    parser.add_argument('-o', '--output', default='voice/test_audio.wav', help='输出WAV文件')
    parser.add_argument('-p', '--p3', default='voice/test_audio.p3', help='输出P3文件')
    parser.add_argument('-d', '--duration', type=float, default=3.0, help='音频时长（秒）')
    parser.add_argument('-f', '--frequency', type=float, default=440.0, help='音频频率（Hz）')
    
    args = parser.parse_args()
    
    # 创建测试音频
    if create_test_audio(args.output, args.duration, args.frequency):
        # 转换为P3格式
        create_p3_format(args.output, args.p3)
