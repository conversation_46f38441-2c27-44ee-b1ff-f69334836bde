# ESP32麦克风到大模型完整数据流文档

## 🎯 概述

本文档详细说明ESP32设备如何将麦克风采集的音频数据传输给大语言模型，并接收AI回复的完整技术流程。

## 🏗️ 系统架构

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────────┐
│   ESP32设备端    │    │    网络传输层     │    │     服务器端        │
├─────────────────┤    ├──────────────────┤    ├─────────────────────┤
│ 1. 麦克风采集    │───▶│ 4. WebSocket/    │───▶│ 7. Opus解码         │
│ 2. VAD语音检测   │    │    MQTT协议      │    │ 8. ASR语音识别      │
│ 3. Opus音频编码  │    │ 5. 实时音频流    │    │ 9. 大语言模型处理    │
│                │    │ 6. 加密传输      │    │ 10. TTS语音合成     │
├─────────────────┤    ├──────────────────┤    ├─────────────────────┤
│ 14. Opus解码    │◀───│ 13. 音频数据     │◀───│ 11. Opus编码        │
│ 15. 扬声器播放   │    │     回传        │    │ 12. 响应发送        │
└─────────────────┘    └──────────────────┘    └─────────────────────┘
```

## 📱 ESP32设备端实现

### 1. 音频采集模块

**文件位置**: `main/application.cc`

```cpp
/**
 * @brief 音频输入处理 - 定时调用（通常10ms一次）
 * 从麦克风硬件读取PCM音频数据
 */
void Application::OnAudioInput() {
    if (audio_processor_->IsRunning()) {
        std::vector<int16_t> data;
        int samples = audio_processor_->GetFeedSize();  // 通常160样本(10ms@16kHz)
        
        if (samples > 0) {
            if (ReadAudio(data, 16000, samples)) {  // 16kHz采样率
                audio_processor_->Feed(data);  // 送入音频处理器
                return;
            }
        }
    }
}

/**
 * @brief 从硬件麦克风读取原始PCM数据
 * @param data 输出的音频数据缓冲区
 * @param sample_rate 采样率(16000Hz)
 * @param samples 需要读取的样本数
 * @return 是否成功读取
 */
bool Application::ReadAudio(std::vector<int16_t>& data, int sample_rate, int samples) {
    auto codec = Board::GetInstance().GetAudioCodec();
    data.resize(samples);
    
    // 从I2S或其他音频接口读取16位PCM数据
    if (!codec->InputData(data)) {
        ESP_LOGE(TAG, "Failed to read audio from codec");
        return false;
    }
    
    ESP_LOGV(TAG, "Read %d audio samples", samples);
    return true;
}
```

### 2. 音频处理和编码模块

```cpp
/**
 * @brief 音频处理器初始化
 * 设置VAD、降噪、Opus编码等功能
 */
void Application::InitializeAudioProcessor() {
    // 创建音频处理器实例
    audio_processor_ = std::make_unique<AudioProcessor>();
    
    // 配置音频处理参数
    AudioProcessorConfig config;
    config.sample_rate = 16000;        // 16kHz采样率
    config.frame_duration_ms = 60;     // 60ms帧长度
    config.enable_vad = true;          // 启用语音活动检测
    config.enable_aec = true;          // 启用回声消除
    config.enable_ns = true;           // 启用降噪
    
    audio_processor_->Initialize(config);
    
    // 设置音频数据回调
    audio_processor_->OnAudioData([this](std::vector<int16_t>&& data) {
        // 在后台线程处理音频编码，避免阻塞主线程
        background_task_->Schedule([this, data = std::move(data)]() mutable {
            ProcessAndSendAudio(std::move(data));
        });
    });
    
    ESP_LOGI(TAG, "Audio processor initialized with Opus encoding");
}

/**
 * @brief 处理和发送音频数据
 * @param pcm_data 16位PCM音频数据
 */
void Application::ProcessAndSendAudio(std::vector<int16_t>&& pcm_data) {
    // 使用Opus编码器压缩音频（16kHz → ~2.4kbps）
    opus_encoder_->Encode(std::move(pcm_data), [this](std::vector<uint8_t>&& opus_data) {
        // 创建音频流数据包
        AudioStreamPacket packet;
        packet.payload = std::move(opus_data);
        packet.sequence = ++audio_sequence_;
        packet.timestamp = esp_timer_get_time() / 1000;  // 毫秒时间戳
        
        // 添加到发送队列
        {
            std::lock_guard<std::mutex> lock(mutex_);
            if (audio_send_queue_.size() >= MAX_AUDIO_PACKETS_IN_QUEUE) {
                audio_send_queue_.pop_front();  // 丢弃最老的包，防止内存溢出
                ESP_LOGW(TAG, "Audio queue full, dropping old packet");
            }
            audio_send_queue_.emplace_back(std::move(packet));
        }
        
        // 触发发送事件
        xEventGroupSetBits(event_group_, SEND_AUDIO_EVENT);
        
        ESP_LOGV(TAG, "Opus packet queued, size: %d bytes", opus_data.size());
    });
}
```

### 3. 网络传输模块

```cpp
/**
 * @brief 主事件循环 - 处理音频发送
 */
void Application::Loop() {
    EventBits_t bits = xEventGroupWaitBits(
        event_group_, 
        SEND_AUDIO_EVENT | DECODE_AUDIO_EVENT | OTHER_EVENTS,
        pdTRUE,    // 清除事件位
        pdFALSE,   // 任意一个事件触发即可
        pdMS_TO_TICKS(100)  // 100ms超时
    );
    
    // 处理音频发送事件
    if (bits & SEND_AUDIO_EVENT) {
        SendQueuedAudioPackets();
    }
    
    // 处理音频解码事件
    if (bits & DECODE_AUDIO_EVENT) {
        DecodeIncomingAudio();
    }
}

/**
 * @brief 发送队列中的音频包
 */
void Application::SendQueuedAudioPackets() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    int sent_count = 0;
    while (!audio_send_queue_.empty()) {
        auto packet = std::move(audio_send_queue_.front());
        audio_send_queue_.pop_front();
        
        // 通过协议层发送（WebSocket或MQTT）
        if (protocol_->SendAudio(packet)) {
            sent_count++;
            ESP_LOGV(TAG, "Audio packet sent, seq: %u, size: %d", 
                    packet.sequence, packet.payload.size());
        } else {
            ESP_LOGE(TAG, "Failed to send audio packet");
            break;  // 发送失败，停止发送剩余包
        }
    }
    
    if (sent_count > 0) {
        ESP_LOGD(TAG, "Sent %d audio packets", sent_count);
    }
}
```

## 🌐 网络协议层实现

### WebSocket协议实现

**文件位置**: `main/protocols/websocket_protocol.cc`

```cpp
/**
 * @brief 通过WebSocket发送音频数据
 * @param packet 包含Opus编码音频的数据包
 * @return 是否发送成功
 */
bool WebsocketProtocol::SendAudio(const AudioStreamPacket& packet) {
    if (!websocket_->IsConnected()) {
        ESP_LOGE(TAG, "WebSocket not connected");
        return false;
    }
    
    // 创建二进制消息头（可选，用于包序号等元数据）
    std::vector<uint8_t> message;
    message.reserve(packet.payload.size() + 8);
    
    // 添加包头信息（小端序）
    uint32_t sequence = packet.sequence;
    uint32_t timestamp = packet.timestamp;
    message.insert(message.end(), (uint8_t*)&sequence, (uint8_t*)&sequence + 4);
    message.insert(message.end(), (uint8_t*)&timestamp, (uint8_t*)&timestamp + 4);
    
    // 添加Opus音频数据
    message.insert(message.end(), packet.payload.begin(), packet.payload.end());
    
    // 发送二进制WebSocket帧
    bool success = websocket_->SendBinary(message.data(), message.size());
    
    if (success) {
        ESP_LOGV(TAG, "WebSocket audio sent: %d bytes", message.size());
    } else {
        ESP_LOGE(TAG, "WebSocket send failed");
    }
    
    return success;
}

/**
 * @brief 处理WebSocket接收到的消息
 * @param data JSON格式的文本消息
 */
void WebsocketProtocol::OnMessage(const std::string& data) {
    cJSON* root = cJSON_Parse(data.c_str());
    if (!root) {
        ESP_LOGE(TAG, "Invalid JSON received");
        return;
    }
    
    auto type = cJSON_GetObjectItem(root, "type");
    if (!cJSON_IsString(type)) {
        cJSON_Delete(root);
        return;
    }
    
    // 处理语音识别结果
    if (strcmp(type->valuestring, "stt") == 0) {
        auto text = cJSON_GetObjectItem(root, "text");
        if (cJSON_IsString(text)) {
            ESP_LOGI(TAG, "STT Result: %s", text->valuestring);
            
            // 通知应用层显示识别结果
            if (on_incoming_json_) {
                on_incoming_json_(root);
            }
        }
    }
    // 处理TTS响应
    else if (strcmp(type->valuestring, "tts") == 0) {
        auto state = cJSON_GetObjectItem(root, "state");
        if (cJSON_IsString(state)) {
            if (strcmp(state->valuestring, "start") == 0) {
                ESP_LOGI(TAG, "TTS playback starting");
            } else if (strcmp(state->valuestring, "sentence_start") == 0) {
                auto text = cJSON_GetObjectItem(root, "text");
                if (cJSON_IsString(text)) {
                    ESP_LOGI(TAG, "AI Response: %s", text->valuestring);
                }
            } else if (strcmp(state->valuestring, "stop") == 0) {
                ESP_LOGI(TAG, "TTS playback finished");
            }
            
            // 转发给应用层处理
            if (on_incoming_json_) {
                on_incoming_json_(root);
            }
        }
    }
    
    cJSON_Delete(root);
}

/**
 * @brief 处理WebSocket接收到的二进制数据（TTS音频）
 * @param data Opus编码的音频数据
 */
void WebsocketProtocol::OnBinaryMessage(const std::vector<uint8_t>& data) {
    ESP_LOGV(TAG, "Received binary audio data: %d bytes", data.size());
    
    // 解析包头（如果有）
    if (data.size() < 8) {
        ESP_LOGE(TAG, "Binary message too short");
        return;
    }
    
    // 提取音频数据（跳过8字节包头）
    AudioStreamPacket packet;
    packet.payload.assign(data.begin() + 8, data.end());
    packet.sequence = *(uint32_t*)data.data();
    packet.timestamp = *(uint32_t*)(data.data() + 4);
    
    // 发送给音频解码器
    if (on_incoming_audio_) {
        on_incoming_audio_(std::move(packet));
    }
}
```

### MQTT协议实现（加密传输）

**文件位置**: `main/protocols/mqtt_protocol.cc`

```cpp
/**
 * @brief 通过MQTT+UDP发送加密音频数据
 * @param packet 音频数据包
 * @return 是否发送成功
 */
bool MqttProtocol::SendAudio(const AudioStreamPacket& packet) {
    std::lock_guard<std::mutex> lock(channel_mutex_);
    
    if (udp_ == nullptr) {
        ESP_LOGE(TAG, "UDP channel not initialized");
        return false;
    }
    
    // 创建加密nonce（随机数+元数据）
    std::string nonce(aes_nonce_);  // 16字节基础nonce
    *(uint16_t*)&nonce[2] = htons(packet.payload.size());    // 数据长度
    *(uint32_t*)&nonce[8] = htonl(packet.timestamp);         // 时间戳
    *(uint32_t*)&nonce[12] = htonl(++local_sequence_);       // 序列号
    
    // 准备加密数据缓冲区
    std::string encrypted;
    encrypted.resize(nonce.size() + packet.payload.size());
    memcpy(encrypted.data(), nonce.data(), nonce.size());
    
    // AES-CTR加密音频数据
    size_t nc_off = 0;
    uint8_t stream_block[16] = {0};
    int ret = mbedtls_aes_crypt_ctr(
        &aes_ctx_,                           // AES上下文
        packet.payload.size(),               // 数据长度
        &nc_off,                            // 偏移量
        (uint8_t*)nonce.c_str(),            // nonce
        stream_block,                       // 流块
        (uint8_t*)packet.payload.data(),    // 输入数据
        (uint8_t*)&encrypted[nonce.size()]  // 输出数据
    );
    
    if (ret != 0) {
        ESP_LOGE(TAG, "AES encryption failed: %d", ret);
        return false;
    }
    
    // 通过UDP发送加密数据
    int sent = udp_->Send(encrypted);
    if (sent > 0) {
        ESP_LOGV(TAG, "Encrypted audio sent: %d bytes", sent);
        return true;
    } else {
        ESP_LOGE(TAG, "UDP send failed");
        return false;
    }
}
```

## 🖥️ 服务器端实现

### Python WebSocket服务器示例

**文件位置**: `server/audio_websocket_server.py`

```python
#!/usr/bin/env python3
"""
ESP32音频WebSocket服务器
处理实时音频流，集成ASR、LLM、TTS
"""

import asyncio
import websockets
import json
import struct
import logging
from typing import Optional, List
import numpy as np

# 音频处理库
import opuslib
import speech_recognition as sr
import pyttsx3
from openai import OpenAI  # 或其他LLM API

class AudioWebSocketServer:
    def __init__(self, host='0.0.0.0', port=8765):
        self.host = host
        self.port = port
        
        # 初始化Opus编解码器
        self.opus_decoder = opuslib.Decoder(fs=16000, channels=1)
        self.opus_encoder = opuslib.Encoder(fs=16000, channels=1, application='voip')
        
        # 初始化语音识别
        self.recognizer = sr.Recognizer()
        
        # 初始化TTS
        self.tts_engine = pyttsx3.init()
        self.tts_engine.setProperty('rate', 150)  # 语速
        
        # 初始化LLM客户端
        self.llm_client = OpenAI(api_key="your-api-key")
        
        # 音频缓冲区
        self.audio_buffer = []
        self.is_recording = False
        
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

    async def handle_client(self, websocket, path):
        """处理单个客户端连接"""
        client_addr = websocket.remote_address
        self.logger.info(f"Client connected: {client_addr}")
        
        try:
            async for message in websocket:
                if isinstance(message, bytes):
                    # 处理二进制音频数据
                    await self.handle_audio_data(websocket, message)
                else:
                    # 处理JSON控制消息
                    await self.handle_json_message(websocket, message)
                    
        except websockets.exceptions.ConnectionClosed:
            self.logger.info(f"Client disconnected: {client_addr}")
        except Exception as e:
            self.logger.error(f"Error handling client {client_addr}: {e}")

    async def handle_audio_data(self, websocket, data: bytes):
        """处理接收到的音频数据"""
        try:
            # 解析包头（8字节：序列号4字节 + 时间戳4字节）
            if len(data) < 8:
                self.logger.warning("Audio packet too short")
                return
                
            sequence, timestamp = struct.unpack('<II', data[:8])
            opus_data = data[8:]
            
            self.logger.debug(f"Received audio: seq={sequence}, ts={timestamp}, size={len(opus_data)}")
            
            # 解码Opus音频为PCM
            try:
                pcm_data = self.opus_decoder.decode(opus_data, frame_size=960)  # 60ms@16kHz
                pcm_array = np.frombuffer(pcm_data, dtype=np.int16)
                
                # 累积音频数据
                self.audio_buffer.extend(pcm_array)
                
                # 简单的VAD：检测音频能量
                audio_energy = np.mean(np.abs(pcm_array))
                
                if audio_energy > 500:  # 阈值需要根据实际情况调整
                    if not self.is_recording:
                        self.is_recording = True
                        self.logger.info("Speech detected, starting recording")
                        
                elif self.is_recording and audio_energy < 200:
                    # 语音结束，处理完整音频
                    self.is_recording = False
                    await self.process_complete_audio(websocket)
                    
            except opuslib.OpusError as e:
                self.logger.error(f"Opus decode error: {e}")
                
        except Exception as e:
            self.logger.error(f"Error processing audio data: {e}")

    async def process_complete_audio(self, websocket):
        """处理完整的语音音频"""
        if len(self.audio_buffer) < 8000:  # 少于0.5秒，忽略
            self.audio_buffer.clear()
            return
            
        try:
            # 转换为音频文件格式
            audio_data = np.array(self.audio_buffer, dtype=np.int16)
            
            # 语音识别
            text = await self.speech_to_text(audio_data)
            if not text:
                self.audio_buffer.clear()
                return
                
            # 发送识别结果给设备
            await websocket.send(json.dumps({
                "type": "stt",
                "text": text
            }))
            
            self.logger.info(f"STT Result: {text}")
            
            # 调用大语言模型
            llm_response = await self.call_llm(text)
            if not llm_response:
                self.audio_buffer.clear()
                return
                
            # 发送TTS开始信号
            await websocket.send(json.dumps({
                "type": "tts",
                "state": "start"
            }))
            
            # 发送AI回复文本（用于屏幕显示）
            await websocket.send(json.dumps({
                "type": "tts",
                "state": "sentence_start",
                "text": llm_response
            }))
            
            self.logger.info(f"LLM Response: {llm_response}")
            
            # 文字转语音
            tts_audio = await self.text_to_speech(llm_response)
            if tts_audio is not None:
                # 发送TTS音频数据
                await self.send_tts_audio(websocket, tts_audio)
            
            # 发送TTS结束信号
            await websocket.send(json.dumps({
                "type": "tts",
                "state": "stop"
            }))
            
        except Exception as e:
            self.logger.error(f"Error processing complete audio: {e}")
        finally:
            self.audio_buffer.clear()

    async def speech_to_text(self, audio_data: np.ndarray) -> Optional[str]:
        """语音识别"""
        try:
            # 转换为speech_recognition库需要的格式
            audio_bytes = audio_data.tobytes()
            
            # 创建AudioData对象
            audio = sr.AudioData(audio_bytes, sample_rate=16000, sample_width=2)
            
            # 使用Google语音识别（可替换为其他引擎）
            text = self.recognizer.recognize_google(audio, language='zh-CN')
            return text.strip()
            
        except sr.UnknownValueError:
            self.logger.warning("Speech recognition could not understand audio")
            return None
        except sr.RequestError as e:
            self.logger.error(f"Speech recognition error: {e}")
            return None

    async def call_llm(self, text: str) -> Optional[str]:
        """调用大语言模型"""
        try:
            response = self.llm_client.chat.completions.create(
                model="gpt-3.5-turbo",  # 或其他模型
                messages=[
                    {"role": "system", "content": "你是一个友好的AI助手，请用简洁的中文回答。"},
                    {"role": "user", "content": text}
                ],
                max_tokens=150,
                temperature=0.7
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            self.logger.error(f"LLM API error: {e}")
            return None

    async def text_to_speech(self, text: str) -> Optional[np.ndarray]:
        """文字转语音"""
        try:
            # 这里使用简单的TTS，实际项目中可能需要更高质量的TTS
            # 可以集成Azure TTS、Google TTS等云服务
            
            # 临时文件方式（简单实现）
            import tempfile
            import wave
            
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as tmp_file:
                self.tts_engine.save_to_file(text, tmp_file.name)
                self.tts_engine.runAndWait()
                
                # 读取生成的音频文件
                with wave.open(tmp_file.name, 'rb') as wav_file:
                    frames = wav_file.readframes(wav_file.getnframes())
                    audio_data = np.frombuffer(frames, dtype=np.int16)
                    
                    # 重采样到16kHz（如果需要）
                    if wav_file.getframerate() != 16000:
                        # 这里需要实现重采样逻辑
                        pass
                        
                    return audio_data
                    
        except Exception as e:
            self.logger.error(f"TTS error: {e}")
            return None

    async def send_tts_audio(self, websocket, audio_data: np.ndarray):
        """发送TTS音频数据"""
        try:
            # 分帧发送（60ms帧）
            frame_size = 960  # 60ms @ 16kHz
            sequence = 0
            
            for i in range(0, len(audio_data), frame_size):
                frame = audio_data[i:i + frame_size]
                
                # 如果最后一帧不足，补零
                if len(frame) < frame_size:
                    frame = np.pad(frame, (0, frame_size - len(frame)), 'constant')
                
                # Opus编码
                opus_data = self.opus_encoder.encode(frame.tobytes(), frame_size)
                
                # 构建包头
                timestamp = int(asyncio.get_event_loop().time() * 1000)
                header = struct.pack('<II', sequence, timestamp)
                
                # 发送二进制数据
                await websocket.send(header + opus_data)
                
                sequence += 1
                
                # 控制发送速率（模拟实时播放）
                await asyncio.sleep(0.06)  # 60ms
                
        except Exception as e:
            self.logger.error(f"Error sending TTS audio: {e}")

    async def handle_json_message(self, websocket, message: str):
        """处理JSON控制消息"""
        try:
            data = json.loads(message)
            msg_type = data.get('type')
            
            if msg_type == 'ping':
                await websocket.send(json.dumps({"type": "pong"}))
            elif msg_type == 'start_recording':
                self.is_recording = True
                self.audio_buffer.clear()
            elif msg_type == 'stop_recording':
                self.is_recording = False
                await self.process_complete_audio(websocket)
                
        except json.JSONDecodeError:
            self.logger.error("Invalid JSON message received")

    def start_server(self):
        """启动WebSocket服务器"""
        self.logger.info(f"Starting audio WebSocket server on {self.host}:{self.port}")
        
        start_server = websockets.serve(
            self.handle_client,
            self.host,
            self.port,
            max_size=10 * 1024 * 1024,  # 10MB最大消息大小
            ping_interval=30,           # 30秒ping间隔
            ping_timeout=10             # 10秒ping超时
        )
        
        asyncio.get_event_loop().run_until_complete(start_server)
        asyncio.get_event_loop().run_forever()

if __name__ == "__main__":
    server = AudioWebSocketServer()
    server.start_server()
```

## 🔧 配置和部署

### ESP32端配置

**文件位置**: `main/Kconfig.projbuild`

```kconfig
menu "Audio Configuration"
    config AUDIO_SAMPLE_RATE
        int "Audio sample rate"
        default 16000
        help
            Audio sample rate in Hz
            
    config OPUS_BITRATE
        int "Opus encoding bitrate"
        default 24000
        help
            Opus encoding bitrate in bps
            
    config AUDIO_FRAME_DURATION_MS
        int "Audio frame duration in milliseconds"
        default 60
        help
            Audio frame duration for processing
endmenu

menu "Server Configuration"
    config WEBSOCKET_SERVER_URL
        string "WebSocket server URL"
        default "ws://*************:8765"
        help
            WebSocket server URL for audio streaming
            
    config MQTT_BROKER_URL
        string "MQTT broker URL"
        default "mqtt://*************:1883"
        help
            MQTT broker URL for audio streaming
endmenu
```

### 服务器端依赖安装

```bash
# 创建Python虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install websockets
pip install opuslib
pip install SpeechRecognition
pip install pyttsx3
pip install openai
pip install numpy
pip install pyaudio  # 用于音频处理
```

## 🐛 调试和故障排除

### 常见问题

1. **音频质量差**
   - 检查麦克风硬件连接
   - 调整ADC增益设置
   - 启用降噪和回声消除

2. **网络延迟高**
   - 减少音频帧大小
   - 使用UDP协议替代TCP
   - 优化服务器处理速度

3. **识别准确率低**
   - 改善音频预处理
   - 使用更好的ASR模型
   - 添加语言模型后处理

### 调试日志配置

**ESP32端**:
```cpp
// 在main/CMakeLists.txt中添加
target_compile_definitions(${COMPONENT_LIB} PRIVATE
    LOG_LOCAL_LEVEL=ESP_LOG_DEBUG
)
```

**服务器端**:
```python
import logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
```

## 📊 性能指标

| 指标 | 目标值 | 说明 |
|------|--------|------|
| 音频延迟 | < 200ms | 端到端语音延迟 |
| 识别准确率 | > 95% | 中文语音识别准确率 |
| 网络带宽 | < 3kbps | Opus编码后的音频流 |
| 内存使用 | < 100KB | ESP32端音频缓冲区 |
| CPU使用率 | < 30% | ESP32端音频处理负载 |

## 🔒 安全考虑

1. **数据加密**: 使用TLS/SSL加密WebSocket连接
2. **身份认证**: 实现设备认证机制
3. **访问控制**: 限制服务器访问权限
4. **数据隐私**: 不存储用户语音数据

## 📈 扩展功能

1. **多语言支持**: 集成多种语言的ASR和TTS
2. **离线模式**: 本地语音处理能力
3. **语音唤醒**: 添加唤醒词检测
4. **情感识别**: 分析语音情感特征
5. **多轮对话**: 维护对话上下文状态

---

**文档版本**: v1.0  
**最后更新**: 2024年12月  
**维护者**: ESP32音频团队