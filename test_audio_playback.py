#!/usr/bin/env python3
# 测试音频播放功能
import struct
import numpy as np
import sounddevice as sd
import argparse
import os

def play_p3_file(input_file):
    """
    播放p3格式的音频文件（简化版本，不使用opus解码）
    """
    print(f"正在播放: {input_file}")
    
    # 音频参数
    sample_rate = 16000
    channels = 1
    
    # 收集所有音频数据
    audio_data = []
    
    try:
        with open(input_file, 'rb') as f:
            while True:
                # 读取头部 (4字节)
                header = f.read(4)
                if not header or len(header) < 4:
                    break
                
                # 解析头部
                packet_type, reserved, data_len = struct.unpack('>BBH', header)
                print(f"包类型: {packet_type}, 保留: {reserved}, 数据长度: {data_len}")
                
                # 读取音频数据（这里假设是原始PCM数据，不是opus）
                pcm_data = f.read(data_len)
                if not pcm_data or len(pcm_data) < data_len:
                    break
                
                # 将字节转换为int16数组
                if len(pcm_data) % 2 == 0:  # 确保是偶数字节
                    audio_array = np.frombuffer(pcm_data, dtype=np.int16)
                    audio_data.extend(audio_array)
                
        if audio_data:
            # 转换为numpy数组并播放
            audio_array = np.array(audio_data, dtype=np.int16)
            print(f"总音频样本数: {len(audio_array)}")
            print(f"音频时长: {len(audio_array) / sample_rate:.2f}秒")
            
            # 播放音频
            sd.play(audio_array, samplerate=sample_rate)
            sd.wait()  # 等待播放完成
            print("播放完成")
        else:
            print("没有找到有效的音频数据")
            
    except Exception as e:
        print(f"播放失败: {e}")

def analyze_p3_file(input_file):
    """分析P3文件的结构"""
    print(f"分析文件: {input_file}")
    
    packet_count = 0
    total_data_size = 0
    
    try:
        with open(input_file, 'rb') as f:
            while True:
                # 读取头部
                header = f.read(4)
                if not header or len(header) < 4:
                    break
                
                packet_type, reserved, data_len = struct.unpack('>BBH', header)
                packet_count += 1
                total_data_size += data_len
                
                if packet_count <= 5:  # 只显示前5个包的详细信息
                    print(f"包 {packet_count}: 类型={packet_type}, 保留={reserved}, 长度={data_len}")
                
                # 跳过数据部分
                f.seek(data_len, 1)
        
        print(f"总包数: {packet_count}")
        print(f"总数据大小: {total_data_size} 字节")
        print(f"文件大小: {os.path.getsize(input_file)} 字节")
        
    except Exception as e:
        print(f"分析失败: {e}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='P3音频文件播放和分析工具')
    parser.add_argument('input_file', help='输入的P3文件')
    parser.add_argument('-a', '--analyze', action='store_true', help='只分析文件结构，不播放')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.input_file):
        print(f"错误: 文件不存在: {args.input_file}")
        exit(1)
    
    if args.analyze:
        analyze_p3_file(args.input_file)
    else:
        play_p3_file(args.input_file)
