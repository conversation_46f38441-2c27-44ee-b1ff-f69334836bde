# ESP32压力传感器与DeepSeek云端大模型集成开发文档

## 项目概述

本文档详细说明如何在ESP32设备上添加压力传感器功能，当传感器被按压时，自动向DeepSeek云端大模型发送指令，并通过语音合成播放返回的响应。

## 硬件需求

### 压力传感器选择
- **推荐型号**: FSR402 压阻式压力传感器
- **工作电压**: 3.3V
- **输出**: 模拟电压信号 (0-3.3V)
- **连接方式**: 分压电路

### 硬件连接图
```
ESP32-S3           压力传感器(FSR402)
GPIO36 (ADC1_CH0) ←→ 传感器一端
3.3V              ←→ 10kΩ电阻 ←→ 传感器另一端
GND               ←→ 10kΩ电阻另一端
```

## 软件架构设计

### 1. 系统组件
- **压力传感器驱动**: 读取ADC值并判断按压状态
- **DeepSeek API客户端**: 发送HTTP请求到云端
- **语音合成模块**: 将文本转换为语音输出
- **事件处理系统**: 管理传感器事件和响应

### 2. 数据流程
```
压力传感器 → ADC读取 → 按压检测 → 发送API请求 → 接收响应 → 语音播放
```

## 代码实现

### 1. 头文件定义 (main/pressure_sensor.h)

```cpp
#ifndef PRESSURE_SENSOR_H
#define PRESSURE_SENSOR_H

#include <esp_adc/adc_oneshot.h>
#include <esp_timer.h>
#include <functional>

class PressureSensor {
public:
    // 构造函数
    PressureSensor(adc_channel_t channel = ADC_CHANNEL_0, 
                   int threshold = 2000, 
                   int debounce_ms = 100);
    
    // 析构函数
    ~PressureSensor();
    
    // 初始化传感器
    esp_err_t Initialize();
    
    // 设置按压回调函数
    void SetPressureCallback(std::function<void(int pressure_value)> callback);
    
    // 开始监控
    void StartMonitoring();
    
    // 停止监控
    void StopMonitoring();
    
    // 获取当前压力值
    int GetPressureValue();
    
private:
    adc_oneshot_unit_handle_t adc_handle_;
    adc_channel_t channel_;
    int pressure_threshold_;
    int debounce_time_ms_;
    bool is_monitoring_;
    bool last_pressed_state_;
    int64_t last_press_time_;
    
    std::function<void(int)> pressure_callback_;
    esp_timer_handle_t monitor_timer_;
    
    // 定时器回调函数
    static void MonitorTimerCallback(void* arg);
    
    // 处理压力检测
    void ProcessPressure();
};

#endif // PRESSURE_SENSOR_H
```

### 2. 压力传感器实现 (main/pressure_sensor.cc)

```cpp
#include "pressure_sensor.h"
#include <esp_log.h>
#include <esp_adc/adc_cali.h>
#include <esp_adc/adc_cali_scheme.h>

static const char* TAG = "PressureSensor";

PressureSensor::PressureSensor(adc_channel_t channel, int threshold, int debounce_ms)
    : adc_handle_(nullptr)
    , channel_(channel)
    , pressure_threshold_(threshold)
    , debounce_time_ms_(debounce_ms)
    , is_monitoring_(false)
    , last_pressed_state_(false)
    , last_press_time_(0)
    , monitor_timer_(nullptr) {
}

PressureSensor::~PressureSensor() {
    StopMonitoring();
    if (adc_handle_) {
        adc_oneshot_del_unit(adc_handle_);
    }
}

esp_err_t PressureSensor::Initialize() {
    // 配置ADC
    adc_oneshot_unit_init_cfg_t init_config = {
        .unit_id = ADC_UNIT_1,
        .ulp_mode = ADC_ULP_MODE_DISABLE,
    };
    
    esp_err_t ret = adc_oneshot_new_unit(&init_config, &adc_handle_);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize ADC unit: %s", esp_err_to_name(ret));
        return ret;
    }
    
    // 配置ADC通道
    adc_oneshot_chan_cfg_t config = {
        .atten = ADC_ATTEN_DB_11,  // 支持0-3.3V输入
        .bitwidth = ADC_BITWIDTH_12,  // 12位分辨率
    };
    
    ret = adc_oneshot_config_channel(adc_handle_, channel_, &config);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to configure ADC channel: %s", esp_err_to_name(ret));
        return ret;
    }
    
    ESP_LOGI(TAG, "Pressure sensor initialized on channel %d", channel_);
    return ESP_OK;
}

void PressureSensor::SetPressureCallback(std::function<void(int)> callback) {
    pressure_callback_ = callback;
}

void PressureSensor::StartMonitoring() {
    if (is_monitoring_) {
        return;
    }
    
    // 创建定时器，每50ms检查一次
    esp_timer_create_args_t timer_args = {
        .callback = MonitorTimerCallback,
        .arg = this,
        .dispatch_method = ESP_TIMER_TASK,
        .name = "pressure_monitor"
    };
    
    esp_err_t ret = esp_timer_create(&timer_args, &monitor_timer_);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to create monitor timer: %s", esp_err_to_name(ret));
        return;
    }
    
    ret = esp_timer_start_periodic(monitor_timer_, 50000); // 50ms
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to start monitor timer: %s", esp_err_to_name(ret));
        return;
    }
    
    is_monitoring_ = true;
    ESP_LOGI(TAG, "Pressure monitoring started");
}

void PressureSensor::StopMonitoring() {
    if (!is_monitoring_) {
        return;
    }
    
    if (monitor_timer_) {
        esp_timer_stop(monitor_timer_);
        esp_timer_delete(monitor_timer_);
        monitor_timer_ = nullptr;
    }
    
    is_monitoring_ = false;
    ESP_LOGI(TAG, "Pressure monitoring stopped");
}

int PressureSensor::GetPressureValue() {
    if (!adc_handle_) {
        return 0;
    }
    
    int adc_raw = 0;
    esp_err_t ret = adc_oneshot_read(adc_handle_, channel_, &adc_raw);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to read ADC: %s", esp_err_to_name(ret));
        return 0;
    }
    
    return adc_raw;
}

void PressureSensor::MonitorTimerCallback(void* arg) {
    PressureSensor* sensor = static_cast<PressureSensor*>(arg);
    sensor->ProcessPressure();
}

void PressureSensor::ProcessPressure() {
    int pressure_value = GetPressureValue();
    bool is_pressed = pressure_value > pressure_threshold_;
    int64_t current_time = esp_timer_get_time() / 1000; // 转换为毫秒
    
    // 防抖处理
    if (is_pressed != last_pressed_state_) {
        if (current_time - last_press_time_ > debounce_time_ms_) {
            last_pressed_state_ = is_pressed;
            last_press_time_ = current_time;
            
            if (is_pressed && pressure_callback_) {
                ESP_LOGI(TAG, "Pressure detected: %d", pressure_value);
                pressure_callback_(pressure_value);
            }
        }
    }
}
```

### 3. DeepSeek API客户端 (main/deepseek_client.h)

```cpp
#ifndef DEEPSEEK_CLIENT_H
#define DEEPSEEK_CLIENT_H

#include <string>
#include <functional>
#include <esp_http_client.h>

class DeepSeekClient {
public:
    // 构造函数
    DeepSeekClient(const std::string& api_key);
    
    // 析构函数
    ~DeepSeekClient();
    
    // 发送压力传感器触发消息
    void SendPressureMessage(int pressure_value, 
                           std::function<void(const std::string& response)> callback);
    
private:
    std::string api_key_;
    std::string api_url_;
    
    // HTTP响应回调
    static esp_err_t HttpEventHandler(esp_http_client_event_t *evt);
    
    // 构建请求JSON
    std::string BuildRequestJson(int pressure_value);
    
    // 解析响应JSON
    std::string ParseResponse(const std::string& json_response);
};

#endif // DEEPSEEK_CLIENT_H
```

### 4. DeepSeek API客户端实现 (main/deepseek_client.cc)

```cpp
#include "deepseek_client.h"
#include <esp_log.h>
#include <cJSON.h>
#include <random>

static const char* TAG = "DeepSeekClient";

// DeepSeek API配置
static const char* DEEPSEEK_API_URL = "https://api.deepseek.com/v1/chat/completions";

struct HttpResponseData {
    std::string data;
    std::function<void(const std::string&)> callback;
};

DeepSeekClient::DeepSeekClient(const std::string& api_key)
    : api_key_(api_key)
    , api_url_(DEEPSEEK_API_URL) {
}

DeepSeekClient::~DeepSeekClient() {
}

void DeepSeekClient::SendPressureMessage(int pressure_value,
                                       std::function<void(const std::string&)> callback) {
    ESP_LOGI(TAG, "Sending pressure message to DeepSeek API, pressure: %d", pressure_value);

    // 构建请求JSON
    std::string request_json = BuildRequestJson(pressure_value);

    // 配置HTTP客户端
    esp_http_client_config_t config = {};
    config.url = api_url_.c_str();
    config.method = HTTP_METHOD_POST;
    config.event_handler = HttpEventHandler;
    config.timeout_ms = 10000;

    esp_http_client_handle_t client = esp_http_client_init(&config);

    // 设置请求头
    esp_http_client_set_header(client, "Content-Type", "application/json");
    std::string auth_header = "Bearer " + api_key_;
    esp_http_client_set_header(client, "Authorization", auth_header.c_str());

    // 设置用户数据
    HttpResponseData* response_data = new HttpResponseData();
    response_data->callback = callback;
    esp_http_client_set_user_data(client, response_data);

    // 发送请求
    esp_http_client_set_post_field(client, request_json.c_str(), request_json.length());

    esp_err_t err = esp_http_client_perform(client);
    if (err == ESP_OK) {
        int status_code = esp_http_client_get_status_code(client);
        ESP_LOGI(TAG, "HTTP POST Status = %d", status_code);

        if (status_code == 200) {
            std::string response_text = ParseResponse(response_data->data);
            if (callback) {
                callback(response_text);
            }
        } else {
            ESP_LOGE(TAG, "HTTP request failed with status: %d", status_code);
            if (callback) {
                callback("抱歉，网络请求失败了");
            }
        }
    } else {
        ESP_LOGE(TAG, "HTTP POST request failed: %s", esp_err_to_name(err));
        if (callback) {
            callback("抱歉，无法连接到服务器");
        }
    }

    delete response_data;
    esp_http_client_cleanup(client);
}

esp_err_t DeepSeekClient::HttpEventHandler(esp_http_client_event_t *evt) {
    HttpResponseData* response_data = static_cast<HttpResponseData*>(evt->user_data);

    switch(evt->event_id) {
        case HTTP_EVENT_ON_DATA:
            if (response_data) {
                response_data->data.append(static_cast<char*>(evt->data), evt->data_len);
            }
            break;
        default:
            break;
    }
    return ESP_OK;
}

std::string DeepSeekClient::BuildRequestJson(int pressure_value) {
    // 随机选择一个压力触发消息
    std::vector<std::string> pressure_messages = {
        "我被按压了！请给我一个有趣的回应",
        "有人按了我，请说点什么让我开心的话",
        "我感受到了压力，请安慰我一下",
        "被按压的感觉真奇妙，请给我讲个笑话",
        "我被触摸了，请用温暖的话语回应我",
        "压力传感器被激活了，请给我一个惊喜回应"
    };

    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, pressure_messages.size() - 1);
    std::string selected_message = pressure_messages[dis(gen)];

    // 构建JSON请求
    cJSON *json = cJSON_CreateObject();
    cJSON *model = cJSON_CreateString("deepseek-chat");
    cJSON *messages = cJSON_CreateArray();
    cJSON *message = cJSON_CreateObject();
    cJSON *role = cJSON_CreateString("user");

    // 添加压力值信息到消息中
    std::string full_message = selected_message + "（压力值：" + std::to_string(pressure_value) + "）";
    cJSON *content = cJSON_CreateString(full_message.c_str());

    cJSON_AddItemToObject(message, "role", role);
    cJSON_AddItemToObject(message, "content", content);
    cJSON_AddItemToArray(messages, message);

    cJSON_AddItemToObject(json, "model", model);
    cJSON_AddItemToObject(json, "messages", messages);
    cJSON_AddNumberToObject(json, "max_tokens", 150);
    cJSON_AddNumberToObject(json, "temperature", 0.8);

    char *json_string = cJSON_Print(json);
    std::string result(json_string);

    free(json_string);
    cJSON_Delete(json);

    return result;
}

std::string DeepSeekClient::ParseResponse(const std::string& json_response) {
    cJSON *json = cJSON_Parse(json_response.c_str());
    if (!json) {
        ESP_LOGE(TAG, "Failed to parse JSON response");
        return "解析响应失败";
    }

    cJSON *choices = cJSON_GetObjectItem(json, "choices");
    if (!choices || !cJSON_IsArray(choices)) {
        ESP_LOGE(TAG, "No choices in response");
        cJSON_Delete(json);
        return "响应格式错误";
    }

    cJSON *first_choice = cJSON_GetArrayItem(choices, 0);
    if (!first_choice) {
        ESP_LOGE(TAG, "No first choice in response");
        cJSON_Delete(json);
        return "响应为空";
    }

    cJSON *message = cJSON_GetObjectItem(first_choice, "message");
    if (!message) {
        ESP_LOGE(TAG, "No message in choice");
        cJSON_Delete(json);
        return "消息格式错误";
    }

    cJSON *content = cJSON_GetObjectItem(message, "content");
    if (!content || !cJSON_IsString(content)) {
        ESP_LOGE(TAG, "No content in message");
        cJSON_Delete(json);
        return "内容格式错误";
    }

    std::string result = content->valuestring;
    cJSON_Delete(json);

    return result;
}
```

### 5. Application类集成 (main/application.h 添加)

```cpp
// 在Application类的private成员中添加：
#include "pressure_sensor.h"
#include "deepseek_client.h"

class Application {
    // ... 现有代码 ...

public:
    // 添加压力传感器相关方法
    void InitializePressureSensor();
    void OnPressureDetected(int pressure_value);
    void OnDeepSeekResponse(const std::string& response);

private:
    // 添加成员变量
    std::unique_ptr<PressureSensor> pressure_sensor_;
    std::unique_ptr<DeepSeekClient> deepseek_client_;

    // DeepSeek API密钥（需要在配置中设置）
    std::string deepseek_api_key_;

    // ... 现有代码 ...
};
```

### 6. Application类实现 (main/application.cc 添加)

```cpp
// 在Application构造函数中添加：
Application::Application() {
    // ... 现有代码 ...

    // 初始化DeepSeek API密钥（从配置或环境变量获取）
    deepseek_api_key_ = "your-deepseek-api-key-here"; // 需要替换为实际的API密钥

    // 创建压力传感器和DeepSeek客户端
    pressure_sensor_ = std::make_unique<PressureSensor>(ADC_CHANNEL_0, 2000, 100);
    deepseek_client_ = std::make_unique<DeepSeekClient>(deepseek_api_key_);
}

// 添加压力传感器初始化方法
void Application::InitializePressureSensor() {
    ESP_LOGI(TAG, "Initializing pressure sensor...");

    esp_err_t ret = pressure_sensor_->Initialize();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize pressure sensor: %s", esp_err_to_name(ret));
        return;
    }

    // 设置压力检测回调
    pressure_sensor_->SetPressureCallback([this](int pressure_value) {
        this->OnPressureDetected(pressure_value);
    });

    // 开始监控
    pressure_sensor_->StartMonitoring();

    ESP_LOGI(TAG, "Pressure sensor initialized and monitoring started");
}

// 压力检测回调处理
void Application::OnPressureDetected(int pressure_value) {
    ESP_LOGI(TAG, "Pressure detected: %d", pressure_value);

    // 在屏幕上显示状态
    auto display = Board::GetInstance().GetDisplay();
    if (display) {
        display->SetStatus("压力检测中");
        display->SetChatMessage("system", "检测到压力，正在请求AI回应...");
        display->SetEmotion("thinking");
        display->ShowNotification("正在连接DeepSeek...", 3000);
    }

    // 发送请求到DeepSeek
    deepseek_client_->SendPressureMessage(pressure_value, [this](const std::string& response) {
        this->OnDeepSeekResponse(response);
    });
}

// DeepSeek响应处理
void Application::OnDeepSeekResponse(const std::string& response) {
    ESP_LOGI(TAG, "DeepSeek response: %s", response.c_str());

    // 更新屏幕显示
    auto display = Board::GetInstance().GetDisplay();
    if (display) {
        display->SetStatus("AI回应中");
        display->SetChatMessage("assistant", response.c_str());
        display->SetEmotion("happy");
        display->ShowNotification("正在播放AI回应...", 2000);
    }

    // 使用TTS播放响应（如果系统支持）
    // 这里需要根据您的TTS系统进行调整
    PlayTextToSpeech(response);
}

// 文本转语音播放（需要根据实际TTS系统实现）
void Application::PlayTextToSpeech(const std::string& text) {
    // 如果系统有TTS功能，在这里调用
    // 例如：tts_engine_->Speak(text);

    // 临时方案：播放提示音表示有响应
    PlaySound(Lang::Sounds::P3_SUCCESS);

    ESP_LOGI(TAG, "TTS would play: %s", text.c_str());
}

// 在Application::Start()方法中添加压力传感器初始化
void Application::Start() {
    // ... 现有启动代码 ...

    // 初始化压力传感器
    InitializePressureSensor();

    // ... 继续现有代码 ...
}
```

## 配置说明

### 1. GPIO引脚配置
- **ADC通道**: GPIO36 (ADC1_CH0)
- **电源**: 3.3V
- **接地**: GND

### 2. 压力阈值调整
```cpp
// 在pressure_sensor构造时调整阈值
pressure_sensor_ = std::make_unique<PressureSensor>(
    ADC_CHANNEL_0,  // GPIO36
    2000,           // 压力阈值 (0-4095)
    100             // 防抖时间 (毫秒)
);
```

### 3. DeepSeek API配置
```cpp
// 需要在代码中设置您的API密钥
deepseek_api_key_ = "sk-your-actual-api-key-here";
```

## 编译配置

### 1. CMakeLists.txt 修改
在 `main/CMakeLists.txt` 中添加：
```cmake
idf_component_register(
    SRCS
        "application.cc"
        "pressure_sensor.cc"
        "deepseek_client.cc"
        # ... 其他源文件
    INCLUDE_DIRS "."
    REQUIRES
        esp_adc
        esp_http_client
        json
        # ... 其他依赖
)
```

### 2. Kconfig 配置
在 `main/Kconfig.projbuild` 中添加：
```
config DEEPSEEK_API_KEY
    string "DeepSeek API Key"
    default ""
    help
        Your DeepSeek API key for accessing the chat API

config PRESSURE_SENSOR_THRESHOLD
    int "Pressure Sensor Threshold"
    default 2000
    range 100 4000
    help
        ADC threshold value for pressure detection (0-4095)
```

## 使用说明

### 1. 硬件安装
1. 将FSR402压力传感器按照连接图连接到ESP32
2. 确保10kΩ下拉电阻正确连接
3. 检查所有连接的稳定性

### 2. 软件配置
1. 获取DeepSeek API密钥
2. 在代码中设置正确的API密钥
3. 根据需要调整压力阈值
4. 编译并烧录固件

### 3. 功能测试
1. 设备启动后，压力传感器自动开始监控
2. 按压传感器，观察串口日志
3. 检查屏幕显示是否更新
4. 验证是否播放了响应音频

## 故障排除

### 1. 传感器无响应
- 检查ADC通道配置是否正确
- 验证硬件连接
- 调整压力阈值

### 2. API请求失败
- 检查网络连接
- 验证API密钥是否正确
- 查看HTTP响应状态码

### 3. 音频播放问题
- 确认TTS系统是否正常工作
- 检查音频输出设备
- 验证音频格式兼容性

## 扩展功能

### 1. 多级压力检测
可以设置多个压力阈值，根据不同的压力强度发送不同的消息。

### 2. 压力模式切换
添加不同的压力响应模式，如"聊天模式"、"学习模式"、"娱乐模式"等。

### 3. 历史记录
保存压力检测和AI响应的历史记录，用于分析和改进。

### 4. 自定义消息
允许用户自定义发送给AI的消息模板。

这个完整的实现方案提供了从硬件连接到软件集成的全套解决方案，您可以根据实际需求进行调整和优化。
