#!/usr/bin/env python3
# 模拟ESP32音频播放功能
import struct
import numpy as np
import sounddevice as sd
import time

class MockAudioProcessor:
    """模拟ESP32的音频处理器"""
    
    def __init__(self):
        self.sample_rate = 16000
        self.is_running = False
        
    def apply_noise_reduction(self, audio_data):
        """模拟降噪处理"""
        print("应用降噪算法...")
        
        # 简单的降噪模拟：应用低通滤波器
        # 这里使用简单的移动平均作为低通滤波器
        window_size = 5
        filtered_data = np.convolve(audio_data, np.ones(window_size)/window_size, mode='same')
        
        # 减少高频噪声
        filtered_data = filtered_data * 0.8  # 稍微降低音量以模拟噪声减少
        
        print("降噪处理完成")
        return filtered_data.astype(np.int16)
    
    def play_audio_with_noise_reduction(self, p3_file):
        """播放带降噪的音频"""
        print(f"开始播放降噪音频: {p3_file}")
        
        # 读取P3文件
        audio_data = []
        with open(p3_file, 'rb') as f:
            while True:
                header = f.read(4)
                if not header or len(header) < 4:
                    break
                
                packet_type, reserved, data_len = struct.unpack('>BBH', header)
                pcm_data = f.read(data_len)
                if not pcm_data or len(pcm_data) < data_len:
                    break
                
                if len(pcm_data) % 2 == 0:
                    audio_array = np.frombuffer(pcm_data, dtype=np.int16)
                    audio_data.extend(audio_array)
        
        if not audio_data:
            print("错误：没有找到音频数据")
            return False
        
        # 转换为numpy数组
        original_audio = np.array(audio_data, dtype=np.int16)
        print(f"原始音频长度: {len(original_audio)} 样本")
        
        # 应用降噪
        denoised_audio = self.apply_noise_reduction(original_audio)
        print(f"降噪后音频长度: {len(denoised_audio)} 样本")
        
        # 播放原始音频
        print("播放原始音频...")
        sd.play(original_audio, samplerate=self.sample_rate)
        sd.wait()
        
        time.sleep(1)  # 短暂停顿
        
        # 播放降噪后的音频
        print("播放降噪后的音频...")
        sd.play(denoised_audio, samplerate=self.sample_rate)
        sd.wait()
        
        print("音频播放完成")
        return True

class MockESP32Device:
    """模拟ESP32设备"""
    
    def __init__(self):
        self.audio_processor = MockAudioProcessor()
        self.boot_button_pressed = False
        
    def on_boot_button_click(self):
        """模拟boot按键点击事件"""
        print("检测到Boot按键点击!")
        print("触发降噪音频播放...")
        
        # 播放降噪测试音频
        success = self.audio_processor.play_audio_with_noise_reduction("voice/test_audio.p3")
        
        if success:
            print("✓ 降噪音频播放成功")
        else:
            print("✗ 降噪音频播放失败")
        
        return success
    
    def simulate_device_operation(self):
        """模拟设备运行"""
        print("=" * 50)
        print("ESP32 音频降噪设备模拟器")
        print("=" * 50)
        print("设备初始化完成")
        print("音频处理器已启动")
        print("等待Boot按键按下...")
        print()
        
        # 模拟按键按下
        input("按Enter键模拟Boot按键按下...")
        return self.on_boot_button_click()

def main():
    """主函数"""
    print("启动ESP32音频降噪测试...")
    
    # 检查测试文件是否存在
    import os
    if not os.path.exists("voice/test_audio.p3"):
        print("错误：测试音频文件不存在")
        print("请先运行 python scripts/create_test_audio.py 创建测试文件")
        return False
    
    # 创建模拟设备
    device = MockESP32Device()
    
    # 运行模拟
    success = device.simulate_device_operation()
    
    if success:
        print("\n✓ 测试完成：降噪功能正常工作")
        print("代码已准备好烧录到ESP32设备")
    else:
        print("\n✗ 测试失败：需要检查代码")
    
    return success

if __name__ == "__main__":
    main()
