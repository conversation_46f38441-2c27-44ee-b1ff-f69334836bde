@echo off
echo ========================================
echo ESP32 音频降噪项目编译和烧录脚本
echo ========================================

REM 检查是否有ESP-IDF环境
echo 检查ESP-IDF环境...

REM 尝试常见的ESP-IDF安装路径
set ESP_IDF_PATHS=C:\Espressif\frameworks\esp-idf-v5.0 C:\Espressif\frameworks\esp-idf-v4.4 C:\esp-idf %USERPROFILE%\esp\esp-idf

for %%p in (%ESP_IDF_PATHS%) do (
    if exist "%%p\export.bat" (
        echo 找到ESP-IDF: %%p
        set IDF_PATH=%%p
        goto :found_idf
    )
)

echo 未找到ESP-IDF安装，尝试使用现有工具...
goto :use_existing_tools

:found_idf
echo 设置ESP-IDF环境...
call "%IDF_PATH%\export.bat"

echo 编译项目...
idf.py build

if %ERRORLEVEL% NEQ 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo 烧录固件...
idf.py -p COM5 flash

if %ERRORLEVEL% NEQ 0 (
    echo 烧录失败！
    pause
    exit /b 1
)

echo 监控串口输出...
idf.py -p COM5 monitor
goto :end

:use_existing_tools
echo 使用现有的esptool进行烧录...
echo 注意：需要先手动编译项目或使用预编译的固件

REM 检查是否有build目录
if not exist "build" (
    echo 错误：没有找到build目录
    echo 请先编译项目或提供预编译的固件
    pause
    exit /b 1
)

REM 检查是否有固件文件
if exist "build\xiaozhi.bin" (
    echo 找到固件文件，开始烧录...
    esptool.exe --chip esp32s3 --port COM5 --baud 460800 write_flash --flash_mode dio --flash_freq 80m --flash_size 16MB 0x0 build\bootloader\bootloader.bin 0x10000 build\xiaozhi.bin 0x8000 build\partition_table\partition-table.bin
) else (
    echo 错误：没有找到固件文件
    echo 请先编译项目
    pause
    exit /b 1
)

:end
echo 完成！
pause
