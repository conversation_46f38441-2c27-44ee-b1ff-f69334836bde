# ESP32 音频降噪项目烧录指南

## 当前状态
✅ 所有代码修改已完成  
✅ 音频文件已准备就绪  
✅ 降噪功能已集成  
⚠️ 需要设置ESP-IDF编译环境  

## 方案一：使用ESP-IDF官方工具链（推荐）

### 1. 安装ESP-IDF
如果还没有安装ESP-IDF，请按以下步骤：

1. 下载ESP-IDF安装器：
   - 访问：https://dl.espressif.com/dl/esp-idf/
   - 下载：`esp-idf-tools-setup-online-2.22.exe`

2. 运行安装器，选择：
   - ESP-IDF版本：v5.0 或更高
   - 安装路径：默认 `C:\Espressif`

3. 安装完成后，桌面会有"ESP-IDF Command Prompt"快捷方式

### 2. 编译和烧录
1. 打开"ESP-IDF Command Prompt"
2. 导航到项目目录：
   ```bash
   cd "D:\BaiduNetdiskDownload\chd-xiaozhi-music_camera_1.7.6"
   ```
3. 编译项目：
   ```bash
   idf.py build
   ```
4. 烧录到设备：
   ```bash
   idf.py -p COM5 flash
   ```
5. 监控串口输出：
   ```bash
   idf.py -p COM5 monitor
   ```

## 方案二：使用现有工具（如果ESP-IDF已安装但环境变量未设置）

### 查找ESP-IDF安装位置
常见位置：
- `C:\Espressif\frameworks\esp-idf-v5.0`
- `C:\Espressif\frameworks\esp-idf-v4.4`
- `%USERPROFILE%\esp\esp-idf`

### 手动设置环境
1. 找到ESP-IDF目录后，运行：
   ```cmd
   cd "C:\Espressif\frameworks\esp-idf-v5.0"
   export.bat
   ```
2. 然后执行编译烧录命令

## 方案三：使用预编译固件（如果有）

如果项目中有预编译的固件文件，可以直接使用esptool烧录：

```bash
esptool.exe --chip esp32s3 --port COM5 --baud 460800 write_flash --flash_mode dio --flash_freq 80m --flash_size 16MB 0x0 build/bootloader/bootloader.bin 0x10000 build/xiaozhi.bin 0x8000 build/partition_table/partition-table.bin
```

## 验证烧录成功

### 1. 硬件连接
- 确保ESP32设备通过USB连接到COM5端口
- 设备应该正常启动，显示屏有内容显示

### 2. 功能测试
1. 等待设备完全启动（约10-15秒）
2. 按下Boot按键（通常在设备侧面或底部）
3. 应该听到从扬声器输出的测试音频（1000Hz正弦波，约3秒）

### 3. 串口监控
如果连接了串口监控，应该看到类似输出：
```
I (12345) ESP_BOX_3: Playing denoised test audio
I (12346) AfeAudioProcessor: Audio communication task started
```

## 故障排除

### 编译错误
1. **找不到idf.py**：
   - 确保ESP-IDF环境已正确设置
   - 运行ESP-IDF的export.bat脚本

2. **依赖组件错误**：
   - 运行：`idf.py reconfigure`
   - 清理后重新编译：`idf.py clean && idf.py build`

### 烧录错误
1. **端口连接失败**：
   - 检查COM5端口是否被其他程序占用
   - 尝试其他波特率：`-b 115200`

2. **设备无响应**：
   - 按住Boot键，然后按Reset键进入下载模式
   - 释放Reset键，再释放Boot键

### 音频播放问题
1. **无声音输出**：
   - 检查扬声器连接
   - 确认音量设置
   - 查看串口输出是否有错误信息

2. **按键无响应**：
   - 确认使用的是Boot按键（不是Reset键）
   - 检查按键是否正常工作

## 项目文件检查清单

在烧录前，确认以下文件已正确修改：

✅ `main/assets/common/denoised_test.p3` - 音频文件存在  
✅ `main/assets/lang_config.h` - 添加了P3_DENOISED_TEST定义  
✅ `main/boards/esp-box-3/esp_box3_board.cc` - 修改了按键处理  
✅ `voice/test_audio.p3` - 测试音频文件正常  

## 下一步

1. 选择上述方案之一进行烧录
2. 测试Boot按键功能
3. 验证音频播放效果
4. 如有问题，查看串口输出进行调试

## 技术支持

如果遇到问题，可以：
1. 查看ESP-IDF官方文档：https://docs.espressif.com/projects/esp-idf/
2. 检查串口输出获取详细错误信息
3. 确认硬件连接和设备状态

**项目状态：代码完成，等待烧录测试** 🚀
