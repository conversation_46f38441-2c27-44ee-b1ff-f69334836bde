# ESP32压力传感器与语音系统集成开发文档

## 项目概述

本文档详细说明如何在现有ESP32语音系统中添加压力传感器功能。基于对现有麦克风处理流程的深入分析，压力传感器将跳过音频处理步骤，直接将生成的文字消息发送给已配置的大语音模型，并通过bool键切换麦克风和压力传感器模式，实现两种输入方式的无冲突协作。

## 系统架构分析

### 现有麦克风处理流程（基于您提供的流程图）

```
1. 麦克风硬件 → ReadAudio() → 16kHz PCM音频数据
   ↓
2. AudioProcessor → VAD检测 → 有效语音片段
   ↓
3. OpusEncoder → Encode() → 压缩的Opus音频包
   ↓
4. Protocol层 → SendAudio() → WebSocket/MQTT发送
   ↓
5. 你的服务器 → Opus解码 → ASR识别 → 文字
   ↓
6. 大语言模型 → 处理文字 → 生成回复文字
   ↓
7. TTS合成 → 回复语音 → Opus编码
   ↓
8. 服务器发送 → WebSocket/MQTT → 设备接收
   ↓
9. 设备解码 → 扬声器播放 → 用户听到AI回复
```

### 压力传感器处理流程（跳过音频处理）

```
1. 压力传感器 → ADC检测 → 压力值
   ↓
2. 生成文字消息 → GetRandomPressureMessage()
   ↓
3. 直接发送文字 → Protocol层 → SendTextMessage()
   ↓
4. 你的服务器 → 跳过ASR → 直接获得文字
   ↓
5. 大语言模型 → 处理文字 → 生成回复文字
   ↓
6. TTS合成 → 回复语音 → Opus编码
   ↓
7. 服务器发送 → WebSocket/MQTT → 设备接收
   ↓
8. 设备解码 → 扬声器播放 → 用户听到AI回复
```

### 模式切换机制

```cpp
bool pressure_sensor_mode = false;  // 模式切换标志

if (pressure_sensor_mode) {
    // 压力传感器模式：直接发送文字
    SendTextMessage(pressure_text);
} else {
    // 麦克风模式：发送音频数据
    SendAudio(audio_packet);
}
```

## 核心设计原则
- **跳过音频处理**: 压力传感器直接生成文字，无需音频编码
- **模式切换控制**: 通过bool变量控制麦克风/压力传感器模式
- **复用协议层**: 使用相同的Protocol接口和TTS播放机制
- **无冲突设计**: 两种模式互斥，不会同时工作

## 硬件连接

### 压力传感器连接图
```
ESP32-S3           压力传感器(FSR402)
GPIO36 (ADC1_CH0) ←→ 传感器一端
3.3V              ←→ 10kΩ电阻 ←→ 传感器另一端  
GND               ←→ 10kΩ电阻另一端
```

## 代码实现

### 1. 压力传感器类定义 (main/pressure_sensor.h)

```cpp
#ifndef PRESSURE_SENSOR_H
#define PRESSURE_SENSOR_H

#include <esp_adc/adc_oneshot.h>
#include <esp_timer.h>
#include <functional>

/**
 * @brief 压力传感器类
 * 负责检测压力变化并触发回调函数
 */
class PressureSensor {
public:
    /**
     * @brief 构造函数
     * @param channel ADC通道 (默认ADC_CHANNEL_0对应GPIO36)
     * @param threshold 压力阈值 (0-4095，默认2000)
     * @param debounce_ms 防抖时间 (毫秒，默认200)
     */
    PressureSensor(adc_channel_t channel = ADC_CHANNEL_0, 
                   int threshold = 2000, 
                   int debounce_ms = 200);
    
    /**
     * @brief 析构函数
     * 清理ADC资源和定时器
     */
    ~PressureSensor();
    
    /**
     * @brief 初始化压力传感器
     * @return ESP_OK 成功，其他值表示失败
     */
    esp_err_t Initialize();
    
    /**
     * @brief 设置压力检测回调函数
     * @param callback 当检测到压力时调用的回调函数，参数为压力值
     */
    void SetPressureCallback(std::function<void(int pressure_value)> callback);
    
    /**
     * @brief 开始压力监控
     * 启动定时器，每50ms检查一次压力状态
     */
    void StartMonitoring();
    
    /**
     * @brief 停止压力监控
     * 停止定时器，释放资源
     */
    void StopMonitoring();
    
    /**
     * @brief 获取当前压力值
     * @return 当前ADC读取的压力值 (0-4095)
     */
    int GetCurrentPressure();

private:
    adc_oneshot_unit_handle_t adc_handle_;      // ADC单元句柄
    adc_channel_t channel_;                     // ADC通道
    int pressure_threshold_;                    // 压力触发阈值
    int debounce_time_ms_;                     // 防抖时间
    bool is_monitoring_;                       // 是否正在监控
    bool last_pressed_state_;                  // 上次按压状态
    int64_t last_press_time_;                  // 上次按压时间
    
    std::function<void(int)> pressure_callback_;  // 压力检测回调函数
    esp_timer_handle_t monitor_timer_;            // 监控定时器
    
    /**
     * @brief 定时器回调函数 (静态函数)
     * @param arg 传递给回调的参数 (PressureSensor实例指针)
     */
    static void MonitorTimerCallback(void* arg);
    
    /**
     * @brief 处理压力检测逻辑
     * 包含防抖处理和状态判断
     */
    void ProcessPressureDetection();
};

#endif // PRESSURE_SENSOR_H
```

### 2. 压力传感器实现 (main/pressure_sensor.cc)

```cpp
#include "pressure_sensor.h"
#include <esp_log.h>
#include <esp_adc/adc_cali.h>
#include <esp_adc/adc_cali_scheme.h>

static const char* TAG = "PressureSensor";

PressureSensor::PressureSensor(adc_channel_t channel, int threshold, int debounce_ms)
    : adc_handle_(nullptr)
    , channel_(channel)
    , pressure_threshold_(threshold)
    , debounce_time_ms_(debounce_ms)
    , is_monitoring_(false)
    , last_pressed_state_(false)
    , last_press_time_(0)
    , monitor_timer_(nullptr) {
    ESP_LOGI(TAG, "PressureSensor created - Channel: %d, Threshold: %d, Debounce: %dms", 
             channel, threshold, debounce_ms);
}

PressureSensor::~PressureSensor() {
    // 停止监控并清理资源
    StopMonitoring();
    if (adc_handle_) {
        adc_oneshot_del_unit(adc_handle_);
        ESP_LOGI(TAG, "ADC unit deleted");
    }
}

esp_err_t PressureSensor::Initialize() {
    ESP_LOGI(TAG, "Initializing pressure sensor...");
    
    // 配置ADC单元
    adc_oneshot_unit_init_cfg_t init_config = {
        .unit_id = ADC_UNIT_1,                    // 使用ADC1单元
        .ulp_mode = ADC_ULP_MODE_DISABLE,         // 禁用ULP模式
    };
    
    esp_err_t ret = adc_oneshot_new_unit(&init_config, &adc_handle_);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize ADC unit: %s", esp_err_to_name(ret));
        return ret;
    }
    
    // 配置ADC通道
    adc_oneshot_chan_cfg_t config = {
        .atten = ADC_ATTEN_DB_11,                 // 11dB衰减，支持0-3.3V输入
        .bitwidth = ADC_BITWIDTH_12,              // 12位分辨率 (0-4095)
    };
    
    ret = adc_oneshot_config_channel(adc_handle_, channel_, &config);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to configure ADC channel %d: %s", channel_, esp_err_to_name(ret));
        return ret;
    }
    
    ESP_LOGI(TAG, "Pressure sensor initialized successfully on GPIO%d", 
             (channel_ == ADC_CHANNEL_0) ? 36 : channel_);
    return ESP_OK;
}

void PressureSensor::SetPressureCallback(std::function<void(int)> callback) {
    pressure_callback_ = callback;
    ESP_LOGI(TAG, "Pressure callback function set");
}

void PressureSensor::StartMonitoring() {
    if (is_monitoring_) {
        ESP_LOGW(TAG, "Monitoring already started");
        return;
    }
    
    ESP_LOGI(TAG, "Starting pressure monitoring...");
    
    // 创建定时器，每50ms检查一次压力状态
    esp_timer_create_args_t timer_args = {
        .callback = MonitorTimerCallback,         // 定时器回调函数
        .arg = this,                             // 传递当前实例指针
        .dispatch_method = ESP_TIMER_TASK,       // 在任务中执行回调
        .name = "pressure_monitor"               // 定时器名称
    };
    
    esp_err_t ret = esp_timer_create(&timer_args, &monitor_timer_);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to create monitor timer: %s", esp_err_to_name(ret));
        return;
    }
    
    // 启动周期性定时器，每50ms触发一次
    ret = esp_timer_start_periodic(monitor_timer_, 50000); // 50ms = 50000微秒
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to start monitor timer: %s", esp_err_to_name(ret));
        return;
    }
    
    is_monitoring_ = true;
    ESP_LOGI(TAG, "Pressure monitoring started (50ms interval)");
}

void PressureSensor::StopMonitoring() {
    if (!is_monitoring_) {
        return;
    }
    
    ESP_LOGI(TAG, "Stopping pressure monitoring...");
    
    // 停止并删除定时器
    if (monitor_timer_) {
        esp_timer_stop(monitor_timer_);
        esp_timer_delete(monitor_timer_);
        monitor_timer_ = nullptr;
    }
    
    is_monitoring_ = false;
    ESP_LOGI(TAG, "Pressure monitoring stopped");
}

int PressureSensor::GetCurrentPressure() {
    if (!adc_handle_) {
        ESP_LOGW(TAG, "ADC not initialized");
        return 0;
    }
    
    int adc_raw = 0;
    esp_err_t ret = adc_oneshot_read(adc_handle_, channel_, &adc_raw);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to read ADC: %s", esp_err_to_name(ret));
        return 0;
    }
    
    return adc_raw;
}

void PressureSensor::MonitorTimerCallback(void* arg) {
    // 静态回调函数，将调用转发给实例方法
    PressureSensor* sensor = static_cast<PressureSensor*>(arg);
    sensor->ProcessPressureDetection();
}

void PressureSensor::ProcessPressureDetection() {
    // 读取当前压力值
    int pressure_value = GetCurrentPressure();
    bool is_pressed = pressure_value > pressure_threshold_;
    int64_t current_time = esp_timer_get_time() / 1000; // 转换为毫秒
    
    // 防抖处理：只有状态改变且超过防抖时间才处理
    if (is_pressed != last_pressed_state_) {
        if (current_time - last_press_time_ > debounce_time_ms_) {
            last_pressed_state_ = is_pressed;
            last_press_time_ = current_time;
            
            // 只在按下时触发回调（不在释放时触发）
            if (is_pressed && pressure_callback_) {
                ESP_LOGI(TAG, "Pressure detected! Value: %d (threshold: %d)",
                         pressure_value, pressure_threshold_);
                pressure_callback_(pressure_value);
            }
        }
    }
}
```

### 3. Application类集成 (main/application.h 修改)

在Application类中添加压力传感器支持：

```cpp
// 在application.h文件顶部添加包含
#include "pressure_sensor.h"
#include <vector>
#include <string>
#include <random>

class Application {
    // ... 现有的public方法 ...

public:
    /**
     * @brief 初始化压力传感器
     * 在Start()方法中调用
     */
    void InitializePressureSensor();

    /**
     * @brief 压力检测回调处理函数
     * @param pressure_value 检测到的压力值
     */
    void OnPressureDetected(int pressure_value);

    /**
     * @brief 发送压力传感器生成的文字消息到云端
     * @param text 要发送的文字消息
     * @return true 发送成功，false 发送失败
     */
    bool SendPressureTextMessage(const std::string& text);

    /**
     * @brief 切换输入模式（麦克风/压力传感器）
     * @param use_pressure_sensor true使用压力传感器模式，false使用麦克风模式
     */
    void SetInputMode(bool use_pressure_sensor);

    /**
     * @brief 获取当前输入模式
     * @return true 压力传感器模式，false 麦克风模式
     */
    bool IsPressureSensorMode() const { return pressure_sensor_mode_; }

private:
    // ... 现有的private成员 ...

    /**
     * @brief 压力传感器实例
     */
    std::unique_ptr<PressureSensor> pressure_sensor_;

    /**
     * @brief 随机数生成器
     */
    std::mt19937 random_generator_;

    /**
     * @brief 输入模式切换标志
     * true: 压力传感器模式，false: 麦克风模式
     */
    bool pressure_sensor_mode_;

    /**
     * @brief 压力传感器处理状态标志
     * 用于跟踪压力传感器是否正在处理请求
     */
    bool pressure_processing_;

    /**
     * @brief 获取随机的压力触发消息
     * @return 随机选择的消息字符串
     */
    std::string GetRandomPressureMessage();
};
```

### 4. Application类实现 (main/application.cc 修改)

```cpp
// 在application.cc文件顶部添加
#include "pressure_sensor.h"

// 在Application构造函数中添加初始化
Application::Application()
    : pressure_sensor_mode_(false)    // 默认使用麦克风模式
    , pressure_processing_(false)     // 初始化压力传感器处理状态
{
    // ... 现有的构造函数代码 ...

    // 初始化随机数生成器
    std::random_device rd;
    random_generator_.seed(rd());

    // 创建压力传感器实例
    // GPIO36 (ADC_CHANNEL_0), 压力阈值2000, 防抖时间200ms
    pressure_sensor_ = std::make_unique<PressureSensor>(ADC_CHANNEL_0, 2000, 200);

    ESP_LOGI(TAG, "Application initialized with pressure sensor support");
    ESP_LOGI(TAG, "Default mode: Microphone (pressure_sensor_mode_ = false)");
}

void Application::InitializePressureSensor() {
    ESP_LOGI(TAG, "Initializing pressure sensor system...");

    // 初始化压力传感器硬件
    esp_err_t ret = pressure_sensor_->Initialize();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize pressure sensor: %s", esp_err_to_name(ret));
        return;
    }

    // 设置压力检测回调函数
    // 使用lambda表达式捕获this指针，调用成员方法
    pressure_sensor_->SetPressureCallback([this](int pressure_value) {
        this->OnPressureDetected(pressure_value);
    });

    // 开始压力监控
    pressure_sensor_->StartMonitoring();

    ESP_LOGI(TAG, "Pressure sensor system initialized and monitoring started");
}

void Application::SetInputMode(bool use_pressure_sensor) {
    pressure_sensor_mode_ = use_pressure_sensor;

    ESP_LOGI(TAG, "Input mode switched to: %s",
             use_pressure_sensor ? "PRESSURE_SENSOR" : "MICROPHONE");

    // 更新屏幕显示当前模式
    auto display = Board::GetInstance().GetDisplay();
    if (display) {
        std::string mode_text = use_pressure_sensor ? "压力传感器模式" : "麦克风模式";
        display->ShowNotification(mode_text.c_str(), 2000);
    }
}

void Application::OnPressureDetected(int pressure_value) {
    ESP_LOGI(TAG, "=== PRESSURE SENSOR TRIGGERED ===");
    ESP_LOGI(TAG, "Detected pressure value: %d", pressure_value);

    // 检查是否在压力传感器模式
    if (!pressure_sensor_mode_) {
        ESP_LOGW(TAG, "Pressure detected but currently in MICROPHONE mode, ignoring");
        return;
    }

    // 检查是否正在处理中
    if (pressure_processing_) {
        ESP_LOGW(TAG, "Pressure sensor is already processing, ignoring new input");
        return;
    }

    // 检查设备状态是否允许处理
    if (device_state_ != kDeviceStateIdle) {
        ESP_LOGW(TAG, "Device is busy (state: %s), cannot handle pressure input",
                 STATE_STRINGS[device_state_]);
        return;
    }

    // 标记压力传感器正在处理
    pressure_processing_ = true;

    // 更新屏幕显示状态
    auto display = Board::GetInstance().GetDisplay();
    if (display) {
        display->SetStatus("压力触发");
        display->SetChatMessage("system", "检测到压力，正在向AI发送消息...");
        display->SetEmotion("thinking");
        display->ShowNotification("压力传感器激活", 2000);
    }

    // 生成随机的压力触发消息
    std::string pressure_message = GetRandomPressureMessage();
    ESP_LOGI(TAG, "Generated pressure message: %s", pressure_message.c_str());

    // 发送文字消息到云端
    bool success = SendPressureTextMessage(pressure_message);
    if (!success) {
        ESP_LOGE(TAG, "Failed to send pressure message");
        pressure_processing_ = false;

        // 显示错误提示
        if (display) {
            display->SetStatus("发送失败");
            display->SetChatMessage("system", "消息发送失败，请检查网络连接");
            display->SetEmotion("sad");
        }
    }
}

bool Application::SendPressureTextMessage(const std::string& text) {
    /**
     * 压力传感器专用的文字消息发送函数
     * 跳过音频处理，直接发送文字给大语音模型
     */
    ESP_LOGI(TAG, "=== SENDING PRESSURE TEXT MESSAGE ===");
    ESP_LOGI(TAG, "Message: %s", text.c_str());

    // 检查协议是否可用
    if (!protocol_) {
        ESP_LOGE(TAG, "Protocol not initialized");
        return false;
    }

    // 确保音频通道已打开（复用现有的通道管理）
    if (!protocol_->IsAudioChannelOpened()) {
        ESP_LOGI(TAG, "Opening audio channel for pressure message");
        SetDeviceState(kDeviceStateConnecting);

        if (!protocol_->OpenAudioChannel()) {
            ESP_LOGE(TAG, "Failed to open audio channel");
            SetDeviceState(kDeviceStateIdle);
            return false;
        }

        ESP_LOGI(TAG, "Audio channel opened successfully");
    }

    // 构建文字消息JSON（参考现有的消息格式）
    // 这里直接发送文字，跳过音频编码步骤
    std::string json_message = "{";
    json_message += "\"session_id\":\"" + session_id_ + "\",";
    json_message += "\"type\":\"text_input\",";        // 标识为文字输入
    json_message += "\"text\":\"" + text + "\",";
    json_message += "\"source\":\"pressure_sensor\"";  // 标识来源为压力传感器
    json_message += "}";

    ESP_LOGI(TAG, "Sending JSON: %s", json_message.c_str());

    // 发送消息到云端（使用现有的协议接口）
    bool success = protocol_->SendText(json_message);
    if (success) {
        ESP_LOGI(TAG, "Pressure text message sent successfully");

        // 设置设备状态为监听（等待响应）
        SetDeviceState(kDeviceStateListening);

        // 更新屏幕显示
        auto display = Board::GetInstance().GetDisplay();
        if (display) {
            display->SetStatus("等待AI回应");
            display->SetChatMessage("user", text.c_str());  // 显示发送的消息
            display->SetEmotion("thinking");
        }
    } else {
        ESP_LOGE(TAG, "Failed to send pressure text message");
        pressure_processing_ = false;
        SetDeviceState(kDeviceStateIdle);
    }

    return success;
}

std::string Application::GetRandomPressureMessage() {
    /**
     * 生成随机的压力触发消息
     * 提供多样化的AI交互体验
     */
    static const std::vector<std::string> pressure_messages = {
        "我被按压了，请给我一个有趣的回应",
        "有人按了我，请说点什么让我开心的话",
        "我感受到了压力，请安慰我一下",
        "被按压的感觉真奇妙，请给我讲个笑话",
        "我被触摸了，请用温暖的话语回应我",
        "压力传感器被激活了，请给我一个惊喜回应",
        "我被轻轻按了一下，请跟我聊聊天",
        "感受到了你的触摸，请给我一些鼓励的话",
        "压力来了，请帮我放松一下心情",
        "被按压让我很兴奋，请说些有趣的事情",
        "我感受到了你的关注，请和我互动一下",
        "压力传感器告诉我有人在关心我，请回应我"
    };

    // 使用随机数生成器选择消息
    std::uniform_int_distribution<size_t> dist(0, pressure_messages.size() - 1);
    size_t index = dist(random_generator_);

    ESP_LOGD(TAG, "Selected message index: %zu/%zu", index, pressure_messages.size());
    return pressure_messages[index];
}

// 在Application::Start()方法中添加压力传感器初始化
void Application::Start() {
    // ... 现有的启动代码 ...

    // 在设备完全启动后初始化压力传感器
    ESP_LOGI(TAG, "Starting pressure sensor initialization...");
    InitializePressureSensor();

    ESP_LOGI(TAG, "System ready - Default mode: MICROPHONE");
    ESP_LOGI(TAG, "Use SetInputMode(true) to switch to PRESSURE_SENSOR mode");

    // ... 继续现有的启动代码 ...
}
```

### 5. 修改现有的TTS响应处理

需要在现有的`OnIncomingJson`回调中添加压力传感器响应处理：

```cpp
// 在Application::Start()中的protocol_->OnIncomingJson回调中修改TTS处理部分：

protocol_->OnIncomingJson([this, display](const cJSON* root) {
    // 解析JSON数据
    auto type = cJSON_GetObjectItem(root, "type");

    if (strcmp(type->valuestring, "tts") == 0) {
        auto state = cJSON_GetObjectItem(root, "state");

        if (strcmp(state->valuestring, "start") == 0) {
            // TTS开始播放
            Schedule([this]() {
                aborted_ = false;
                if (device_state_ == kDeviceStateIdle || device_state_ == kDeviceStateListening) {
                    SetDeviceState(kDeviceStateSpeaking);
                }
            });
        } else if (strcmp(state->valuestring, "stop") == 0) {
            // TTS播放结束
            Schedule([this]() {
                background_task_->WaitForCompletion();
                if (device_state_ == kDeviceStateSpeaking) {
                    // 检查是否是压力传感器触发的会话
                    if (pressure_processing_) {
                        ESP_LOGI(TAG, "Pressure sensor TTS completed");
                        pressure_processing_ = false;  // 重置处理状态

                        // 压力传感器会话结束，返回空闲状态
                        SetDeviceState(kDeviceStateIdle);

                        // 显示完成状态
                        auto display = Board::GetInstance().GetDisplay();
                        if (display) {
                            display->SetStatus("压力响应完成");
                            display->SetEmotion("happy");
                            display->ShowNotification("AI回应完成", 2000);
                        }
                    } else {
                        // 正常的麦克风会话处理
                        if (listening_mode_ == kListeningModeManualStop) {
                            SetDeviceState(kDeviceStateIdle);
                        } else {
                            SetDeviceState(kDeviceStateListening);
                        }
                    }
                }
            });
        } else if (strcmp(state->valuestring, "sentence_start") == 0) {
            // AI开始说话，显示文字
            auto text = cJSON_GetObjectItem(root, "text");
            if (cJSON_IsString(text)) {
                ESP_LOGI(TAG, "<< %s", text->valuestring);
                Schedule([this, display, message = std::string(text->valuestring)]() {
                    display->SetChatMessage("assistant", message.c_str());

                    // 如果是压力传感器触发的响应，添加特殊标识
                    if (pressure_processing_) {
                        display->ShowNotification("压力传感器AI回应", 1000);
                    }
                });
            }
        }
    } else if (strcmp(type->valuestring, "stt") == 0) {
        // 语音识别结果（仅麦克风模式）
        auto text = cJSON_GetObjectItem(root, "text");
        if (cJSON_IsString(text)) {
            ESP_LOGI(TAG, ">> %s", text->valuestring);
            Schedule([this, display, message = std::string(text->valuestring)]() {
                display->SetChatMessage("user", message.c_str());
            });
        }
    }

    // ... 其他现有的处理代码 ...
});
```

### 6. 编译配置修改

#### CMakeLists.txt 修改
在 `main/CMakeLists.txt` 中添加新的源文件：

```cmake
idf_component_register(
    SRCS
        "application.cc"
        "pressure_sensor.cc"        # 添加压力传感器源文件
        # ... 其他现有源文件
    INCLUDE_DIRS "."
    REQUIRES
        esp_adc                     # 添加ADC依赖
        esp_timer                   # 添加定时器依赖
        # ... 其他现有依赖
)
```

#### Kconfig 配置添加
在 `main/Kconfig.projbuild` 中添加配置选项：

```
menu "Pressure Sensor Configuration"
    config PRESSURE_SENSOR_ENABLE
        bool "Enable Pressure Sensor"
        default y
        help
            Enable pressure sensor functionality

    config PRESSURE_SENSOR_GPIO
        int "Pressure Sensor GPIO Pin"
        default 36
        range 0 39
        depends on PRESSURE_SENSOR_ENABLE
        help
            GPIO pin for pressure sensor (ADC capable pin)

    config PRESSURE_SENSOR_THRESHOLD
        int "Pressure Detection Threshold"
        default 2000
        range 100 4000
        depends on PRESSURE_SENSOR_ENABLE
        help
            ADC threshold value for pressure detection (0-4095)

    config PRESSURE_SENSOR_DEBOUNCE_MS
        int "Pressure Sensor Debounce Time (ms)"
        default 200
        range 50 1000
        depends on PRESSURE_SENSOR_ENABLE
        help
            Debounce time in milliseconds to avoid false triggers
endmenu
```

## 使用说明

### 1. 硬件安装步骤
1. **准备材料**: FSR402压力传感器、10kΩ电阻、面包板、杜邦线
2. **连接电路**: 按照连接图将传感器连接到GPIO36
3. **测试连接**: 使用万用表检查连接是否正确
4. **固定传感器**: 将传感器固定在合适的位置

### 2. 软件配置步骤
1. **添加源文件**: 将pressure_sensor.h和pressure_sensor.cc添加到项目
2. **修改Application**: 按照文档修改application.h和application.cc
3. **更新编译配置**: 修改CMakeLists.txt和Kconfig
4. **配置参数**: 根据需要调整阈值和防抖时间
5. **编译烧录**: 编译项目并烧录到设备

### 3. 模式切换使用

#### 默认麦克风模式
```cpp
// 系统启动后默认为麦克风模式
// pressure_sensor_mode_ = false
// 此时压力传感器检测到压力会被忽略
```

#### 切换到压力传感器模式
```cpp
// 在代码中调用
Application::GetInstance().SetInputMode(true);

// 或者通过按键切换（需要在按键处理中添加）
void SomeButtonHandler() {
    auto& app = Application::GetInstance();
    bool current_mode = app.IsPressureSensorMode();
    app.SetInputMode(!current_mode);  // 切换模式
}
```

### 4. 功能测试

#### 测试压力传感器硬件
```cpp
// 在串口监控中查看压力值
// 按压传感器时应该看到类似日志：
// I (12345) PressureSensor: Current pressure: 2500 (threshold: 2000)
// I (12346) PressureSensor: Pressure detected! Value: 2500
```

#### 测试模式切换
```cpp
// 查看模式切换日志
// I (12345) Application: Input mode switched to: PRESSURE_SENSOR
// I (12346) Application: Input mode switched to: MICROPHONE
```

#### 测试完整流程
1. **切换到压力传感器模式**: `SetInputMode(true)`
2. **按压传感器**: 观察串口日志和屏幕显示
3. **验证消息发送**: 查看"SENDING PRESSURE TEXT MESSAGE"日志
4. **验证AI回复**: 听取扬声器播放的AI回应
5. **验证状态恢复**: 确认回到空闲状态

## 调试和故障排除

### 1. 压力传感器无响应
```cpp
// 添加调试日志查看压力值
void Application::DebugPressureValue() {
    if (pressure_sensor_) {
        int value = pressure_sensor_->GetCurrentPressure();
        ESP_LOGI(TAG, "Current pressure value: %d", value);
    }
}
```

### 2. 模式切换问题
```cpp
// 检查当前模式
ESP_LOGI(TAG, "Current mode: %s",
         IsPressureSensorMode() ? "PRESSURE_SENSOR" : "MICROPHONE");
```

### 3. 消息发送失败
```cpp
// 检查协议状态
if (!protocol_) {
    ESP_LOGE(TAG, "Protocol not initialized");
} else if (!protocol_->IsAudioChannelOpened()) {
    ESP_LOGE(TAG, "Audio channel not opened");
}
```

### 4. 常见问题解决

#### 问题1: 压力传感器一直触发
**原因**: 阈值设置过低或传感器有干扰
**解决**: 增加pressure_threshold_值或检查硬件连接

#### 问题2: 压力传感器不触发
**原因**: 阈值设置过高或硬件连接问题
**解决**: 降低pressure_threshold_值或检查电路连接

#### 问题3: AI不回应
**原因**: 网络连接问题或消息格式错误
**解决**: 检查网络连接和JSON消息格式

#### 问题4: 模式切换无效
**原因**: 模式标志未正确设置
**解决**: 确认SetInputMode()调用和pressure_sensor_mode_状态

## 完整使用流程示例

### 启动和初始化
```cpp
void setup() {
    // 系统启动
    Application::GetInstance().Start();

    // 此时默认为麦克风模式
    // 压力传感器已初始化但不会响应
}
```

### 切换到压力传感器模式
```cpp
void switch_to_pressure_mode() {
    auto& app = Application::GetInstance();

    // 切换到压力传感器模式
    app.SetInputMode(true);

    // 屏幕会显示："压力传感器模式"
    // 现在按压传感器会触发AI对话
}
```

### 压力传感器交互流程
```
1. 用户按压传感器
   ↓
2. 屏幕显示："检测到压力，正在向AI发送消息..."
   ↓
3. 生成随机消息："我被按压了，请给我一个有趣的回应"
   ↓
4. 发送文字到云端（跳过音频编码）
   ↓
5. 屏幕显示："等待AI回应"
   ↓
6. 接收AI回复并播放语音
   ↓
7. 屏幕显示："压力响应完成"
   ↓
8. 返回空闲状态，可以进行下次交互
```

### 切换回麦克风模式
```cpp
void switch_to_microphone_mode() {
    auto& app = Application::GetInstance();

    // 切换回麦克风模式
    app.SetInputMode(false);

    // 屏幕会显示："麦克风模式"
    // 现在可以使用语音唤醒和对话
}
```

## 总结

这个压力传感器集成方案的核心优势：

1. **跳过音频处理**: 直接发送文字，提高响应速度
2. **模式切换控制**: 通过bool变量实现麦克风/压力传感器无冲突切换
3. **复用现有架构**: 使用相同的协议层和TTS播放机制
4. **完整的状态管理**: 确保设备状态正确转换
5. **详细的日志记录**: 便于调试和问题排查
6. **灵活的配置选项**: 支持阈值、防抖时间等参数调整

通过这个方案，您可以在现有的语音系统基础上，无缝添加压力传感器功能，实现两种输入方式的协调工作。
